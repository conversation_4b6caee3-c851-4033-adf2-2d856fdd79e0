\documentclass[journal]{IEEEtran}

% *** PACKAGES ***
\usepackage{cite}
\usepackage{graphicx}
\graphicspath{{fig/}}  % 设置图片搜索路径
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{algorithmic}
\usepackage{array}
\usepackage{mdwmath}
\usepackage{mdwtab}
\usepackage{eqparbox}
\usepackage{url}
\usepackage{subfigure}
\usepackage{multirow}
\usepackage{booktabs}
\usepackage{color}
\usepackage{balance}
\usepackage{fontspec}
\usepackage{xeCJK}
% *** PDF, URL AND HYPERLINK PACKAGES ***
\usepackage[xetex,colorlinks=true,bookmarks=false]{hyperref}

% correct bad hyphenation here
\hyphenation{op-tical net-works semi-conduc-tor}

\begin{document}

% paper title
\title{基于物理约束状态空间模型的多时相遥感图像橡胶树冠高精度分割方法}

% author names and affiliations
\author{Firstname~Lastname,~\IEEEmembership{Member,~IEEE,}
        Secondname~Lastname,~\IEEEmembership{Senior~Member,~IEEE,}
        and~Thirdname~Lastname,~\IEEEmembership{Fellow,~IEEE}
\thanks{F. Lastname is with the Department of Remote Sensing, University of Agriculture, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{S. Lastname is with the Institute of Geoscience and Remote Sensing, Research Center, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{T. Lastname is with the Department of Computer Science, Technology University, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{Manuscript received Month DD, YYYY; revised Month DD, YYYY.}}

% make the title area
\maketitle

\begin{abstract}
高精度橡胶树冠分割是热带经济作物遥感监测的核心技术挑战。传统方法在处理复杂森林环境中的模糊边界、物种多样性和时相变化时存在显著局限。本文提出了一种融合物理约束的状态空间模型框架，专门用于多时相遥感图像的橡胶树冠精确分割。该框架包含三个核心创新：(1) GM-Mamba模块，首次将状态空间模型引入遥感图像分割，通过傅里叶域增强和选择性扫描机制实现长程空间依赖建模；(2) MPC-Poisson约束模块，基于扩散方程的物理先验抑制非目标植被干扰；(3) MASA-Optimizer，通过多智能体协作解决跨时相学习中的灾难性遗忘问题。在六个不同生态环境的遥感数据集上的实验表明，该方法在边界精度、形状保真度和时间一致性方面显著优于现有方法，AP50达到76.63\%，在落叶期样本仅占训练集4.7%的情况下仍保持91.16%的年度计数精度。该研究为遥感图像分割提供了新的理论框架，推动了物理信息神经网络在遥感领域的应用发展。
\end{abstract}

\begin{IEEEkeywords}
遥感；橡胶树冠分割；状态空间模型；物理信息神经网络；多时相分析；图像分割；森林遥感
\end{IEEEkeywords}

\IEEEpeerreviewmaketitle

\section{引言}
准确的单株树冠分割对于森林资源调查、生态系统评估和精准林业管理具有重要意义。橡胶种植园作为重要的经济林，其单株树冠的精确识别对于割胶计划制定、产量预测和病虫害防控等关键环节至关重要\cite{priyadarshan2017biology}。然而，橡胶树独特的生物学特性给遥感监测带来了特殊挑战。与天然森林相比，橡胶种植园虽然具有规则的行列式分布和相对一致的树龄，但其极端的季节性变化使得遥感图像解译变得异常困难。在高分辨率遥感影像中，橡胶树冠层在不同物候期呈现截然不同的特征：落叶期冠层覆盖度急剧下降至20\%以下，萌芽期新叶呈现红褐色反射，成熟期密集的枝叶交错形成复杂的阴影模式。这些剧烈的物候变化使得基于单一时相训练的模型在其他物候期性能显著下降，严重制约了橡胶林的自动化监测。

针对树冠分割任务，已有研究发展了两大类方法：传统方法和深度学习方法。根据Ke和Quackenbush\cite{ke2011review}的综述，传统树冠分割方法主要包括三种类型：谷线跟踪、区域生长和分水岭分割。谷线跟踪方法依赖森林内部的阴影间隙来分割树冠\cite{gougeon1998automatic}，在成熟针叶林中表现良好，但在自遮蔽树冠或复杂森林条件下精度下降。区域生长方法广泛用于树冠分割，通常利用树冠的光谱特征进行分割\cite{culvenor2002tida, erikson2003segmentation}。该算法需要种子点开始生长和停止生长的准则，但在复杂混交林中准确检测树顶和确定最优阈值都很困难。分水岭分割方法主要基于图像中的边缘信息，但图像噪声会导致过分割问题。

为改善传统分水岭分割的过分割问题，研究者提出了两类改进方法。第一类是多尺度分析方法。Jing等人\cite{jing2012automated}基于三个主要尺度的高斯滤波器平滑图像进行多尺度分水岭分割，然后从不同尺度的结果中选择最优分割。Yang等人\cite{yang2014automated}基于计算的梯度图像进行分水岭分割，采用多尺度阈值生成多尺度分割结果。第二类是标记控制分水岭分割，其中标记是基于局部最大值检测的树顶。Wang等人\cite{wang2004automated, wang2010crown}检测了两种不同类型的树顶：光谱局部最大值和空间局部最大值。Lamar等人\cite{lamar2005automated}和Tong等人\cite{tong2021improved}利用空间局部最大值检测树顶。

近年来，深度学习方法被广泛应用于树冠分割任务\cite{zhao2023review}。一类深度学习方法将深度学习与分水岭分割相结合。例如，Lassalle等人\cite{lassalle2022cnn}利用CNN模型计算距离图，指示像素到最近树冠边界的距离，然后通过在距离图中定位局部最大值来识别树顶。Freudenberg等人\cite{freudenberg2022individual}利用U-Net\cite{ronneberger2015u}预测树冠掩膜、树冠轮廓和距离图。另一类深度学习方法采用实例分割技术，其中Mask R-CNN\cite{he2017mask}是树冠分割研究中使用最广泛的深度学习模型\cite{braga2020amazon, hao2021individual, ball2023detectree2}。

尽管深度学习方法通常优于传统方法，但在橡胶树冠分割中仍面临三个关键挑战。首先，树冠轮廓不清晰问题。成熟橡胶树冠幅可达8-12米，相邻树木的枝叶经常交错重叠，形成连续的冠层覆盖，单株树木的边界变得极其模糊。传统的边缘检测算法难以准确提取单株树冠边界，深度学习模型也容易产生欠分割现象。其次，物候动态引发的特征漂移问题。橡胶树在落叶期、萌芽期、生长期和成熟期表现出截然不同的光谱反射特征，多时相NDVI曲线呈现非连续性变化，使得基于单一时相训练的模型在其他物候期性能急剧下降。第三，非目标植被的干扰问题。橡胶园常见的飞机草、薇甘菊等杂草在近红外波段的反射特征与橡胶幼树高度相似，形状相似性使得杂草的叶片形态与橡胶幼树相近，传统的光谱分析方法无法有效区分，导致严重的误分类现象。

针对上述挑战，本文提出了一种基于物理约束状态空间模型的橡胶树冠分割框架（CSAF）。该框架专门设计来解决橡胶树遥感监测中的三个核心问题：1）针对树冠轮廓不清晰问题，我们设计了基于状态空间模型的边界增强模块（GM-Mamba），首次将状态空间模型引入树冠分割任务，通过傅里叶域增强和选择性扫描机制建模树冠边界的长程空间依赖关系，有效捕获橡胶树叶片沿枝条方向的连续性特征；2）针对物候动态引发的特征漂移问题，我们开发了多智能体持续学习机制（MASA-Optimizer），通过自适应记忆管理和经验回放避免跨物候期训练中的灾难性遗忘，确保模型在极端物候变化下的稳定性；3）针对非目标植被干扰问题，我们构建了基于泊松方程的物理约束模块（MPC-Poisson），利用扩散过程的形态学先验约束分割结果的空间连贯性和结构合理性，有效抑制形状相似、纹理相近、分布模式复杂的杂草干扰。



为评估所提出方法的性能，我们在构建的跨物候期橡胶树数据集上进行了全面实验，该数据集涵盖萌芽期、生长期、成熟期和落叶期四个完整阶段。与广泛认可的基准模型相比，我们的CSAF框架在RT-Set数据集上实现了AP50为76.63\%的性能，相比基线模型提升了6.11个百分点。特别值得注意的是，在落叶期样本仅占训练集4.7\%的极端不平衡情况下，CSAF在年度计数任务中仍保持91.16\%的平均精度，显著优于对比方法的63.75\%。此外，我们还分析了超参数设置和图像空间分辨率对分割精度的影响，为未来用户在分割任务中应用所提出模型提供指导。

本文的其余部分组织如下：第2节提供了研究区域描述、所提出树冠分割方法的详细信息以及树冠分割评估指标的介绍。第3节展示了实验结果，包括分割结果描述、分割误差分析以及与其他方法的比较。第4节分析了所提出分割方法的参数设置，分析了空间分辨率的影响，探讨了森林研究的前景，并提出了可能的进一步改进。第5节为研究提供了总体结论。





\section{材料与方法}

\subsection{研究区域}
研究区域位于中国海南省儋州市西北部，地理坐标为19°31'50.59''N，109°28'52.62''E，隶属于中国热带农业科学院橡胶研究所建立的实验林区。该区域属于南亚热带季风气候区，气候特征为全年温暖，年内温差较小。年平均气温介于22.5°C至25.6°C之间，其中1月为最冷月，7月为最热月。年日照时数为1780-2600小时，年降水量在900-2400毫米之间，为橡胶树生长提供了良好的水热条件\cite{priyadarshan2017biology}。

值得注意的是，海南岛位于南海北缘，是经常受台风影响的沿海岛屿之一。主要台风季节为6月至10月。虽然儋州位于岛屿西北部，远离典型的台风登陆区，但仍经常受到热带气旋及其外围云系的影响。这些扰动包括极端风力和强降雨，常常造成树木倒伏和冠层破碎等结构性损害。作为典型的热带经济作物，橡胶树的冠层结构对风力扰动高度敏感。因此，在该区域进行野外观测和遥感研究，不仅能够收集不同扰动强度下的冠层响应数据，还为评估所提出的树冠分割和生态监测框架在复杂背景条件下的稳健性和泛化能力提供了理想的测试平台。此外，该区域为评估框架在极端天气情景下的适应性和监测精度提供了合适的环境，在灾害响应和森林健康评估方面具有巨大的应用潜力。研究区域的具体位置如图\ref{fig:study_area}所示。

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\columnwidth]{Study Area.pdf}
    \caption{研究区域位置图。}
    \label{fig:study_area}
\end{figure}

\subsection{数据集构建}
为了全面评估所提出方法的性能和泛化能力，本研究在四个涵盖不同地理环境和树种的数据集上进行了广泛实验，包括热带橡胶种植园、温带果树园、城市绿化带和北方针叶林等多样化生态系统。表\ref{tab:datasets}详细描述了各数据集的基本信息。

\begin{table*}[htbp]
\renewcommand{\arraystretch}{1.2}
\caption{实验数据集详细信息}
\label{tab:datasets}
\centering
\scriptsize
\begin{tabular}{p{2.0cm}p{1.8cm}p{2.2cm}p{1.8cm}p{2.0cm}p{1.8cm}p{2.0cm}}
\toprule
\textbf{数据集} & \textbf{地理位置} & \textbf{主要树种} & \textbf{图像数量} & \textbf{数据来源} & \textbf{采集时间} & \textbf{主要特征} \\
\midrule
橡胶树数据集 & 中国海南 & \textit{Hevea} & 5,281 & UAV (80m) & 2023.11- & 极端物候 \\
(RT-Set) & 儋州 & \textit{brasiliensis} & (3697/ & Phantom 4 & 2024.07 & 变化，四个 \\
 & 19°31'N &  & 1056/528) & RTK &  & 完整周期 \\
\midrule
杨梅树数据集 & 中国浙江 & \textit{Myrica} & 2,430 & UAV & 2023.05- & 球形冠层 \\
(BT-Set) & 永嘉 & \textit{rubra} & (1701/ & DJI & 2024.04 & 结构，强 \\
 & 28°17'N &  & 486/243) & Phantom 4 &  & 阴影效应 \\
\midrule
城市树冠 & 西班牙 & 混合阔叶/ & 1,420 & 机载传感器 & 2022.06- & 复杂城市 \\
数据集 & 莱里达 & 针叶 & (994/ & 高分辨率 & 2023.09 & 背景，人工 \\
(UT-Set) & 41°37'N &  & 284/142) & 航拍 &  & 修剪形状 \\
\midrule
魁北克森林 & 加拿大 & \textit{Picea} & 5,321 & 公开数据集 & 2023.07- & 高密度针 \\
(CTF-Set) & 魁北克 & spp.混合 & (仅测试集) & 温带混 & 2024.06 & 叶林，24个 \\
 & 45°59'N & 落叶/针叶 &  & 交林 &  & 树种 \\
\bottomrule
\end{tabular}
\end{table*}

\textbf{橡胶树数据集（RT-Set）}：该数据集采集自中国海南省儋州市中国热带农业科学院橡胶研究所建立的实验林区，涵盖萌芽期、生长期、成熟期和落叶期四个完整物候周期。数据采集使用DJI Phantom 4 RTK无人机，配备1英寸CMOS传感器（2000万有效像素），飞行高度80米，横向和纵向重叠率均为85\%。该数据集的独特之处在于其极端的季节性变化特征，落叶期样本仅占训练集4.7\%，为评估模型在物候动态条件下的稳健性提供了理想的测试平台。

\textbf{杨梅树数据集（BT-Set）}：采集自中国浙江省永嘉县大洋山森林公园（28°17'N–28°19'N, 120°26'E–120°28'E），该区域属于中亚热带常绿阔叶林带。杨梅树具有独特的球形冠层结构和密集的分枝模式，但强阴影效应和密集的林下植被与树冠具有高度光谱相似性，使得该数据集在实例分割任务中极具挑战性。

\textbf{城市树冠数据集（UT-Set）}：来源于西班牙莱里达市（41°37'N, 0°37'E）的地中海城市环境，涵盖59公顷的异质城市基础设施。该数据集包含多种城市环境，如主要街道、住宅区、公园和密集建筑区，提供了广泛的场景多样性和结构遮挡。数据集包含14,772个标注树冠，其中许多被建筑物、车辆或街道设施部分遮挡。

\textbf{魁北克森林数据集（CTF-Set）}：采集自加拿大魁北克省圣伊波利特（45°59'N, 74°00'W）的温带混交林，研究区域跨越丘陵、湿地和湖泊等多样地形，支持典型的北美未管理森林中的多种落叶和针叶树种。该数据集的关键挑战在于冠层内的高物种多样性，包含十多个树种和属，具有独特但经常重叠的冠层结构。

表\ref{tab:phenology}进一步详细描述了RT-Set数据集中不同物候期的样本分布情况，展现了橡胶树极端季节性变化的数据特征。

\begin{table}[htbp]
\renewcommand{\arraystretch}{1.2}
\caption{橡胶树数据集物候期分布}
\label{tab:phenology}
\centering
\begin{tabular}{lccc}
\toprule
\textbf{物候期} & \textbf{训练集} & \textbf{验证集} & \textbf{测试集} \\
\midrule
萌芽期 & 1,074 & 271 & 131 \\
生长期 & 1,381 & 286 & 136 \\
成熟期 & 1,068 & 243 & 120 \\
落叶期 & 174 & 256 & 141 \\
\midrule
\textbf{总计} & \textbf{3,697} & \textbf{1,056} & \textbf{528} \\
\bottomrule
\end{tabular}
\end{table}

\section{Methodology}

\subsection{总体架构}

本文提出的基于物理约束状态空间模型的橡胶树冠分割框架（CSAF, Constrained State-space Architecture Framework）采用端到端的深度学习架构，专门设计用于解决多时相遥感图像中橡胶树冠的精确分割问题。如图\ref{fig:framework}所示，CSAF框架由四个核心模块组成：特征提取骨干网络、GM-Mamba边界增强模块、MASA-Optimizer持续学习机制和MPC-Poisson物理约束模块。

\begin{figure*}[htbp]
    \centering
    \includegraphics[width=\textwidth]{All.emf}
    \caption{CSAF框架总体架构图。(A) 预处理阶段包括图像裁剪、亮度对比度调整和骨干网络特征提取；(B) GM-Mamba模块通过多尺度梯度编码和空间建模增强树冠边界；(C) MASA-Optimizer采用多智能体学习策略解决跨物种和物候期的灾难性遗忘问题；(D) MPC-Poisson通过基于泊松方程的约束嵌入形状感知先验，抑制背景干扰；(E) 输出包括精确的树冠掩膜、植被指数热图(SCVI)和树级数据分析。}
    \label{fig:}
\end{figure*}

\textbf{特征提取骨干网络}：采用改进的ResNet-50作为特征提取骨干网络，通过特征金字塔网络(FPN)生成多尺度特征表示$\{F_{p2}, F_{p3}, F_{p4}, F_{p5}, F_{p6}\}$，其中$F_{pi} \in \mathbb{R}^{B \times C \times H_i \times W_i}$表示第$i$层的特征图，$B$为批次大小，$C=256$为特征通道数。为适应橡胶树冠的多尺度特性，我们在骨干网络中集成了拉普拉斯金字塔变换，增强边缘信息的提取能力。

\textbf{数据流处理}：输入的多时相遥感图像$I \in \mathbb{R}^{B \times T \times 3 \times H \times W}$首先经过预处理模块，包括几何校正、辐射校正和数据增强。其中$T$表示时相数量，$H \times W$为图像空间分辨率。预处理后的特征通过骨干网络提取得到多层次特征表示，随后分别输入到三个核心模块进行并行处理。

\textbf{模块间协作机制}：三个核心模块通过耦合数据驱动与物理约束优化引擎(CDPO-E, Coupled Data-driven and Physics-constrained Optimization Engine)实现协同工作。CDPO-E由MASA-Optimizer和MPC-Poisson共同构成，通过物理引导的损失项在MASA-Optimizer中传播，动态调整遗忘系数$\alpha$，实现两个模块间的协调优化。

具体而言，设输入特征为$\mathcal{X} \in \mathbb{R}^{B \times L \times D}$，其中$L$为序列长度，$D$为特征维度。特征首先被分解为主流和残差流：
\begin{equation}
\mathcal{X}_{primary}, \mathcal{X}_{residual} = \text{Projection}(\mathcal{X})
\end{equation}

主流通过1D卷积和状态空间模型进行结构化状态转换，残差流保留上下文信息。两个流的输出融合为统一表示后传递给CDPO-E引擎：
\begin{equation}
\mathcal{F}_{fused} = \text{Fusion}(\text{SSM}(\mathcal{X}_{primary}), \mathcal{X}_{residual})
\end{equation}

\textbf{输出生成}：框架最终输出包括三个部分：(1) 精确的树冠实例分割掩膜，用于单株树木的精确定位和边界提取；(2) 植被指数热图(SCVI, Spectral Crown Vegetation Index)，提供树冠健康状态的定量评估；(3) 树级统计数据，包括树冠面积、周长、形状指数等几何参数，支持后续的生态分析和管理决策。

该架构设计充分考虑了橡胶树遥感监测的特殊需求，通过物理约束与深度学习的有机结合，在保证分割精度的同时提升了模型的可解释性和泛化能力。

\subsection{GM-Mamba边界增强模块}

GM-Mamba模块(Gradient-enhanced Mamba)是本文的核心创新之一，首次将状态空间模型引入遥感图像分割任务，专门用于解决橡胶树冠边界模糊的问题。该模块通过傅里叶域增强和选择性扫描机制实现长程空间依赖建模，有效捕获橡胶树叶片沿枝条方向的连续性特征。

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\columnwidth]{gm_mamba.pdf}
    \caption{GM-Mamba模块架构图。该模块集成了基于傅里叶变换的频域去噪、基于拉普拉斯金字塔的多尺度边缘增强，以及基于Mamba的长程依赖建模。从频域的实部和虚部提取双尺度梯度线索，实现从粗到细的边缘检测。输出传入选择性扫描增强的SSM，通过动态状态更新捕获方向性树冠结构。融合输出增强边界连续性和树冠级空间一致性。}
    \label{fig:gm_mamba}
\end{figure}

如图\ref{fig:gm_mamba}所示，GM-Mamba模块包含三个关键组件：

\textbf{频域梯度增强}：针对橡胶树冠边界在空域中容易受噪声干扰的问题，我们首先将输入特征$F \in \mathbb{R}^{B \times C \times H \times W}$转换到频域进行处理。通过二维快速傅里叶变换(2D-FFT)获得频域表示：
\begin{equation}
\mathcal{F}_{freq} = \text{FFT2D}(F) = \mathcal{F}_{real} + i\mathcal{F}_{imag}
\end{equation}

从实部和虚部分别提取梯度信息，构建双尺度梯度线索：
\begin{equation}
\begin{aligned}
G_{coarse} &= \nabla \mathcal{F}_{real} \\
G_{fine} &= \nabla \mathcal{F}_{imag}
\end{aligned}
\end{equation}

通过逆傅里叶变换将增强的梯度信息转换回空域，实现从粗到细的边缘检测。

\textbf{多尺度拉普拉斯金字塔}：为了捕获不同尺度的树冠边界特征，我们构建拉普拉斯金字塔$\{L_0, L_1, L_2, L_3\}$，其中每一层$L_i$通过高斯滤波和下采样操作获得：
\begin{equation}
L_i = G_i - \text{Upsample}(G_{i+1})
\end{equation}

其中$G_i$表示第$i$层高斯金字塔。多尺度特征通过自适应权重融合：
\begin{equation}
F_{multi} = \sum_{i=0}^{3} w_i \cdot L_i, \quad \sum_{i=0}^{3} w_i = 1
\end{equation}

\textbf{选择性扫描状态空间模型}：这是GM-Mamba的核心组件，基于Mamba架构设计的选择性状态空间模型。输入特征首先被展平为序列格式$\chi \in \mathbb{R}^{B \times L \times D}$，然后通过选择性扫描机制进行处理：
\begin{equation}
\begin{aligned}
\Delta, B, C &= \text{Split}(\text{Linear}(\chi)) \\
\Delta &= \text{Softplus}(\text{Linear}(\Delta)) \\
A &= -\exp(\text{Parameter}(D, N))
\end{aligned}
\end{equation}

状态空间模型的离散化通过零阶保持器(ZOH)实现：
\begin{equation}
\begin{aligned}
\bar{A} &= \exp(\Delta A) \\
\bar{B} &= (\Delta A)^{-1}(\bar{A} - I) \Delta B
\end{aligned}
\end{equation}

最终的输出通过递归状态更新获得：
\begin{equation}
\begin{aligned}
h_t &= \bar{A} h_{t-1} + \bar{B} x_t \\
y_t &= C h_t + D x_t
\end{aligned}
\end{equation}

该设计使模型能够有效建模树冠边界的长程空间依赖关系，特别适合捕获橡胶树叶片沿枝条方向的连续性特征。

\subsection{MPC-Poisson物理约束模块}

MPC-Poisson模块(Morphology-constrained Physics-informed Poisson)是本文提出的物理信息神经网络组件，专门用于抑制非目标植被干扰。该模块基于扩散方程的物理先验，利用泊松方程约束分割结果的空间连贯性和结构合理性。

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\columnwidth]{mpc_poisson.pdf}
    \caption{MPC-Poisson模块架构图。该模块通过四层MLP将归一化空间坐标映射为树冠存在的概率场$\gamma(x,y)$。基于泊松方程导出的物理信息损失项惩罚偏离预期扩散模式的情况，从而抑制不规则的非目标植被并增强树冠掩膜的连贯性。}
    \label{fig:mpc_poisson}
\end{figure}

如图\ref{fig:mpc_poisson}所示，MPC-Poisson模块的设计基于以下物理观察：与杂草等非目标植被的不规则形状不同，树冠通常表现出类似扩散的结构特征。受此启发，我们引入基于泊松方程的形态学约束。

\textbf{物理模型构建}：设树冠存在概率场为$\gamma(x,y)$，其中$(x,y)$为归一化的空间坐标。根据扩散理论，树冠的空间分布应满足泊松方程：
\begin{equation}
\nabla^2 \gamma(x,y) = \frac{\partial^2 \gamma}{\partial x^2} + \frac{\partial^2 \gamma}{\partial y^2} = f(x,y)
\end{equation}

其中$f(x,y)$为源项，表示树冠生长的驱动力。

\textbf{神经网络实现}：MPC-Poisson模块采用四层多层感知机(MLP)实现，网络结构为：
\begin{equation}
\text{MLP}: \mathbb{R}^2 \rightarrow \mathbb{R}^{h_1} \rightarrow \mathbb{R}^{h_2} \rightarrow \mathbb{R}^{h_3} \rightarrow \mathbb{R}^1
\end{equation}

其中$h_1=64, h_2=32, h_3=16$为隐藏层维度。网络使用Swish激活函数以保证梯度的平滑性：
\begin{equation}
\text{Swish}(x) = x \cdot \sigma(x)
\end{equation}

\textbf{物理信息损失}：通过自动微分计算泊松方程的残差，构建物理信息损失：
\begin{equation}
\begin{aligned}
\frac{\partial \gamma}{\partial x} &= \frac{\partial \text{MLP}(x,y)}{\partial x} \\
\frac{\partial \gamma}{\partial y} &= \frac{\partial \text{MLP}(x,y)}{\partial y} \\
\frac{\partial^2 \gamma}{\partial x^2} &= \frac{\partial^2 \text{MLP}(x,y)}{\partial x^2} \\
\frac{\partial^2 \gamma}{\partial y^2} &= \frac{\partial^2 \text{MLP}(x,y)}{\partial y^2}
\end{aligned}
\end{equation}

泊松方程残差为：
\begin{equation}
R_{poisson} = f_{source} - D_{iff} \cdot \left(\frac{\partial^2 \gamma}{\partial x^2} + \frac{\partial^2 \gamma}{\partial y^2}\right)
\end{equation}

其中$D_{iff}=0.1$为扩散系数，$f_{source}=-1.0$为源项强度。

\textbf{总损失函数}：MPC-Poisson模块的总损失函数包含三个部分：
\begin{equation}
\mathcal{L}_{total} = \mathcal{L}_{physics} + \mathcal{L}_{data} + \lambda_{BC} \mathcal{L}_{boundary}
\end{equation}

其中：
\begin{equation}
\begin{aligned}
\mathcal{L}_{physics} &= \mathbb{E}[R_{poisson}^2] \\
\mathcal{L}_{data} &= \mathbb{E}[(\gamma - \gamma_{gt})^2] \\
\mathcal{L}_{boundary} &= \mathbb{E}[(\gamma_{boundary} - \gamma_{bc})^2]
\end{aligned}
\end{equation}

$\lambda_{BC}=0.001$为边界条件权重，$\gamma_{gt}$为真实标签，$\gamma_{bc}$为边界条件。

该模块通过物理约束有效抑制了形状不规则的杂草干扰，显著提升了橡胶树冠分割的准确性和鲁棒性。

\subsection{MASA-Optimizer持续学习机制}

MASA-Optimizer(Multi-Agent Simulated Annealing Optimizer)是专门设计用于解决跨物候期学习中灾难性遗忘问题的多智能体持续学习机制。该模块通过协作式多智能体控制策略管理可学习的遗忘系数$\alpha$，在历史知识保留与新数据适应之间实现动态平衡。

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\columnwidth]{masa_optimizer.pdf}
    \caption{MASA-Optimizer框架概览图。该模块在持续学习中集成模拟退火、强化学习和遗传搜索三个优化阶段，动态调整遗忘因子$\alpha$，从而在历史和新颖特征表示之间保持平衡。经验回放和自适应更新使得在物种和物候变异性条件下能够鲁棒融合多时相冠层特征。}
    \label{fig:masa_optimizer}
\end{figure}

如图\ref{fig:masa_optimizer}所示，MASA-Optimizer采用三阶段优化策略，每个阶段采用不同的智能体算法：

\textbf{阶段一：模拟退火初始化}：在训练初期，使用模拟退火算法初始化遗忘系数$\alpha$。设当前温度为$T_t$，接受概率为：
\begin{equation}
P_{accept} = \exp\left(-\frac{\Delta E}{T_t}\right)
\end{equation}

其中$\Delta E$为能量变化，定义为损失函数的变化量。温度按指数衰减：
\begin{equation}
T_{t+1} = \beta \cdot T_t, \quad 0 < \beta < 1
\end{equation}

遗忘系数的更新规则为：
\begin{equation}
\alpha_{t+1} = \begin{cases}
\alpha_{proposed} & \text{if } P_{accept} > \text{rand}(0,1) \\
\alpha_t & \text{otherwise}
\end{cases}
\end{equation}

\textbf{阶段二：强化学习优化}：在中期训练阶段，采用Q-learning算法优化遗忘系数。状态空间$S$定义为当前的特征表示，动作空间$A$为遗忘系数的调整量。Q值更新公式为：
\begin{equation}
Q(s_t, a_t) \leftarrow Q(s_t, a_t) + \eta [r_t + \gamma \max_{a'} Q(s_{t+1}, a') - Q(s_t, a_t)]
\end{equation}

其中$\eta$为学习率，$\gamma$为折扣因子，$r_t$为即时奖励，定义为：
\begin{equation}
r_t = -\mathcal{L}_{current} - \lambda_{forget} \mathcal{L}_{forget}
\end{equation}

$\mathcal{L}_{current}$为当前任务损失，$\mathcal{L}_{forget}$为遗忘损失，$\lambda_{forget}$为平衡权重。

\textbf{阶段三：遗传算法精调}：在训练后期，使用遗传算法进行精细调优。种群中每个个体表示一个候选的$\alpha$值。适应度函数定义为：
\begin{equation}
\text{Fitness}(\alpha) = \frac{1}{1 + \mathcal{L}_{total}(\alpha)}
\end{equation}

选择操作采用轮盘赌选择，交叉操作使用算术交叉：
\begin{equation}
\begin{aligned}
\alpha_{offspring1} &= \lambda \alpha_{parent1} + (1-\lambda) \alpha_{parent2} \\
\alpha_{offspring2} &= (1-\lambda) \alpha_{parent1} + \lambda \alpha_{parent2}
\end{aligned}
\end{equation}

变异操作添加高斯噪声：
\begin{equation}
\alpha_{mutated} = \alpha + \mathcal{N}(0, \sigma^2)
\end{equation}

\textbf{经验回放机制}：为了防止灾难性遗忘，MASA-Optimizer维护一个经验缓冲区$\mathcal{B}$，存储历史时相的特征表示。在每次训练迭代中，从缓冲区随机采样历史样本与当前样本混合训练：
\begin{equation}
\mathcal{L}_{replay} = \alpha \mathcal{L}_{current} + (1-\alpha) \mathcal{L}_{historical}
\end{equation}

\textbf{自适应特征融合}：经过三阶段优化后，遗忘系数$\alpha$收敛到最优值。此时模型能够有效适应物种和物候的动态变化，在相应特征权重之间达到最优平衡：
\begin{equation}
\mathcal{F}_{fused} = \alpha \mathcal{F}_{new} + (1-\alpha) \mathcal{F}_{memory}
\end{equation}

其中$\mathcal{F}_{new}$为新时相特征，$\mathcal{F}_{memory}$为记忆特征。

MASA-Optimizer的集成显著增强了模型融合异质特征的能力，即使在物种间和物候变异性较大以及样本分布不平衡的条件下，也能提升分割框架的鲁棒性和泛化能力。

\section{Experimental Setup}


\section*{Acknowledgment}

The authors would like to thank the anonymous reviewers for their valuable comments and suggestions.

% references section
\bibliographystyle{IEEEtran}
\bibliography{ieee_tgrs_references}

\begin{IEEEbiography}{Firstname Lastname}
received the B.S. degree in remote sensing from University A in 2015, and the Ph.D. degree in geoscience and remote sensing from University B in 2020. He is currently a Research Scientist with the Institute of Remote Sensing. His research interests include deep learning, plant phenotyping, and precision agriculture.
\end{IEEEbiography}

\begin{IEEEbiography}{Secondname Lastname}
received the Ph.D. degree in computer science from University C in 2018. She is currently an Associate Professor with the Department of Remote Sensing. Her research focuses on machine learning applications in agriculture and environmental monitoring.
\end{IEEEbiography}

\begin{IEEEbiography}{Thirdname Lastname}
is a Professor and Director of the Remote Sensing Laboratory. His research interests include hyperspectral remote sensing, crop monitoring, and precision agriculture technologies.
\end{IEEEbiography}

\end{document}
