\documentclass[journal]{IEEEtran}

% *** PACKAGES ***
\usepackage{cite}
\usepackage{graphicx}
\graphicspath{{fig/}}  % 设置图片搜索路径
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{algorithmic}
\usepackage{array}
\usepackage{mdwmath}
\usepackage{mdwtab}
\usepackage{eqparbox}
\usepackage{url}
\usepackage{subfigure}
\usepackage{multirow}
\usepackage{booktabs}
\usepackage{color}
\usepackage{balance}
\usepackage{fontspec}
\usepackage{xeCJK}
% *** PDF, URL AND HYPERLINK PACKAGES ***
\usepackage[xetex,colorlinks=true,bookmarks=false]{hyperref}

% correct bad hyphenation here
\hyphenation{op-tical net-works semi-conduc-tor}

\begin{document}

% paper title
\title{基于物理约束状态空间模型的多时相遥感图像橡胶树冠高精度分割方法}

% author names and affiliations
\author{Firstname~Lastname,~\IEEEmembership{Member,~IEEE,}
        Secondname~Lastname,~\IEEEmembership{Senior~Member,~IEEE,}
        and~Thirdname~Lastname,~\IEEEmembership{Fellow,~IEEE}
\thanks{F. Lastname is with the Department of Remote Sensing, University of Agriculture, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{S. Lastname is with the Institute of Geoscience and Remote Sensing, Research Center, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{T. Lastname is with the Department of Computer Science, Technology University, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{Manuscript received Month DD, YYYY; revised Month DD, YYYY.}}

% make the title area
\maketitle

\begin{abstract}
高精度橡胶树冠分割是热带经济作物遥感监测的核心技术挑战。传统方法在处理复杂森林环境中的模糊边界、物种多样性和时相变化时存在显著局限。本文提出了一种融合物理约束的状态空间模型框架，专门用于多时相遥感图像的橡胶树冠精确分割。该框架包含三个核心创新：(1) GM-Mamba模块，首次将状态空间模型引入遥感图像分割，通过傅里叶域增强和选择性扫描机制实现长程空间依赖建模；(2) MPC-Poisson约束模块，基于扩散方程的物理先验抑制非目标植被干扰；(3) MASA-Optimizer，通过多智能体协作解决跨时相学习中的灾难性遗忘问题。在六个不同生态环境的遥感数据集上的实验表明，该方法在边界精度、形状保真度和时间一致性方面显著优于现有方法，AP50达到76.63\%，在落叶期样本仅占训练集4.7%的情况下仍保持91.16%的年度计数精度。该研究为遥感图像分割提供了新的理论框架，推动了物理信息神经网络在遥感领域的应用发展。
\end{abstract}

\begin{IEEEkeywords}
遥感；橡胶树冠分割；状态空间模型；物理信息神经网络；多时相分析；图像分割；森林遥感
\end{IEEEkeywords}

\IEEEpeerreviewmaketitle

\section{引言}
准确的单株树冠分割对于森林资源调查、生态系统评估和精准林业管理具有重要意义。橡胶种植园作为重要的经济林，其单株树冠的精确识别对于割胶计划制定、产量预测和病虫害防控等关键环节至关重要\cite{priyadarshan2017biology}。然而，橡胶树独特的生物学特性给遥感监测带来了特殊挑战。与天然森林相比，橡胶种植园虽然具有规则的行列式分布和相对一致的树龄，但其极端的季节性变化使得遥感图像解译变得异常困难。在高分辨率遥感影像中，橡胶树冠层在不同物候期呈现截然不同的特征：落叶期冠层覆盖度急剧下降至20\%以下，萌芽期新叶呈现红褐色反射，成熟期密集的枝叶交错形成复杂的阴影模式。这些剧烈的物候变化使得基于单一时相训练的模型在其他物候期性能显著下降，严重制约了橡胶林的自动化监测。

针对树冠分割任务，已有研究发展了两大类方法：传统方法和深度学习方法。根据Ke和Quackenbush\cite{ke2011review}的综述，传统树冠分割方法主要包括三种类型：谷线跟踪、区域生长和分水岭分割。谷线跟踪方法依赖森林内部的阴影间隙来分割树冠\cite{gougeon1998automatic}，在成熟针叶林中表现良好，但在自遮蔽树冠或复杂森林条件下精度下降。区域生长方法广泛用于树冠分割，通常利用树冠的光谱特征进行分割\cite{culvenor2002tida, erikson2003segmentation}。该算法需要种子点开始生长和停止生长的准则，但在复杂混交林中准确检测树顶和确定最优阈值都很困难。分水岭分割方法主要基于图像中的边缘信息，但图像噪声会导致过分割问题。

为改善传统分水岭分割的过分割问题，研究者提出了两类改进方法。第一类是多尺度分析方法。Jing等人\cite{jing2012automated}基于三个主要尺度的高斯滤波器平滑图像进行多尺度分水岭分割，然后从不同尺度的结果中选择最优分割。Yang等人\cite{yang2014automated}基于计算的梯度图像进行分水岭分割，采用多尺度阈值生成多尺度分割结果。第二类是标记控制分水岭分割，其中标记是基于局部最大值检测的树顶。Wang等人\cite{wang2004automated, wang2010crown}检测了两种不同类型的树顶：光谱局部最大值和空间局部最大值。Lamar等人\cite{lamar2005automated}和Tong等人\cite{tong2021improved}利用空间局部最大值检测树顶。

近年来，深度学习方法被广泛应用于树冠分割任务\cite{zhao2023review}。一类深度学习方法将深度学习与分水岭分割相结合。例如，Lassalle等人\cite{lassalle2022cnn}利用CNN模型计算距离图，指示像素到最近树冠边界的距离，然后通过在距离图中定位局部最大值来识别树顶。Freudenberg等人\cite{freudenberg2022individual}利用U-Net\cite{ronneberger2015u}预测树冠掩膜、树冠轮廓和距离图。另一类深度学习方法采用实例分割技术，其中Mask R-CNN\cite{he2017mask}是树冠分割研究中使用最广泛的深度学习模型\cite{braga2020amazon, hao2021individual, ball2023detectree2}。

尽管深度学习方法通常优于传统方法，但在橡胶树冠分割中仍面临三个关键挑战。首先，树冠轮廓不清晰问题。成熟橡胶树冠幅可达8-12米，相邻树木的枝叶经常交错重叠，形成连续的冠层覆盖，单株树木的边界变得极其模糊。传统的边缘检测算法难以准确提取单株树冠边界，深度学习模型也容易产生欠分割现象。其次，物候动态引发的特征漂移问题。橡胶树在落叶期、萌芽期、生长期和成熟期表现出截然不同的光谱反射特征，多时相NDVI曲线呈现非连续性变化，使得基于单一时相训练的模型在其他物候期性能急剧下降。第三，非目标植被的干扰问题。橡胶园常见的飞机草、薇甘菊等杂草在近红外波段的反射特征与橡胶幼树高度相似，形状相似性使得杂草的叶片形态与橡胶幼树相近，传统的光谱分析方法无法有效区分，导致严重的误分类现象。

针对上述挑战，本文提出了一种基于物理约束状态空间模型的橡胶树冠分割框架（CSAF）。该框架专门设计来解决橡胶树遥感监测中的三个核心问题：1）针对树冠轮廓不清晰问题，我们设计了基于状态空间模型的边界增强模块（GM-Mamba），首次将状态空间模型引入树冠分割任务，通过傅里叶域增强和选择性扫描机制建模树冠边界的长程空间依赖关系，有效捕获橡胶树叶片沿枝条方向的连续性特征；2）针对物候动态引发的特征漂移问题，我们开发了多智能体持续学习机制（MASA-Optimizer），通过自适应记忆管理和经验回放避免跨物候期训练中的灾难性遗忘，确保模型在极端物候变化下的稳定性；3）针对非目标植被干扰问题，我们构建了基于泊松方程的物理约束模块（MPC-Poisson），利用扩散过程的形态学先验约束分割结果的空间连贯性和结构合理性，有效抑制形状相似、纹理相近、分布模式复杂的杂草干扰。



为评估所提出方法的性能，我们在构建的跨物候期橡胶树数据集上进行了全面实验，该数据集涵盖萌芽期、生长期、成熟期和落叶期四个完整阶段。与广泛认可的基准模型相比，我们的CSAF框架在RT-Set数据集上实现了AP50为76.63\%的性能，相比基线模型提升了6.11个百分点。特别值得注意的是，在落叶期样本仅占训练集4.7\%的极端不平衡情况下，CSAF在年度计数任务中仍保持91.16\%的平均精度，显著优于对比方法的63.75\%。此外，我们还分析了超参数设置和图像空间分辨率对分割精度的影响，为未来用户在分割任务中应用所提出模型提供指导。

本文的其余部分组织如下：第2节提供了研究区域描述、所提出树冠分割方法的详细信息以及树冠分割评估指标的介绍。第3节展示了实验结果，包括分割结果描述、分割误差分析以及与其他方法的比较。第4节分析了所提出分割方法的参数设置，分析了空间分辨率的影响，探讨了森林研究的前景，并提出了可能的进一步改进。第5节为研究提供了总体结论。





\section{材料与方法}

\subsection{研究区域}
研究区域位于中国海南省儋州市西北部，地理坐标为19°31'50.59''N，109°28'52.62''E，隶属于中国热带农业科学院橡胶研究所建立的实验林区。该区域属于南亚热带季风气候区，气候特征为全年温暖，年内温差较小。年平均气温介于22.5°C至25.6°C之间，其中1月为最冷月，7月为最热月。年日照时数为1780-2600小时，年降水量在900-2400毫米之间，为橡胶树生长提供了良好的水热条件\cite{priyadarshan2017biology}。

值得注意的是，海南岛位于南海北缘，是经常受台风影响的沿海岛屿之一。主要台风季节为6月至10月。虽然儋州位于岛屿西北部，远离典型的台风登陆区，但仍经常受到热带气旋及其外围云系的影响。这些扰动包括极端风力和强降雨，常常造成树木倒伏和冠层破碎等结构性损害。作为典型的热带经济作物，橡胶树的冠层结构对风力扰动高度敏感。因此，在该区域进行野外观测和遥感研究，不仅能够收集不同扰动强度下的冠层响应数据，还为评估所提出的树冠分割和生态监测框架在复杂背景条件下的稳健性和泛化能力提供了理想的测试平台。此外，该区域为评估框架在极端天气情景下的适应性和监测精度提供了合适的环境，在灾害响应和森林健康评估方面具有巨大的应用潜力。研究区域的具体位置如图\ref{fig:study_area}所示。

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\columnwidth]{Study Area.pdf}
    \caption{研究区域位置图。}
    \label{fig:study_area}
\end{figure}

\subsection{数据集构建}
为了全面评估所提出方法的性能和泛化能力，本研究在四个涵盖不同地理环境和树种的数据集上进行了广泛实验，包括热带橡胶种植园、温带果树园、城市绿化带和北方针叶林等多样化生态系统。表\ref{tab:datasets}详细描述了各数据集的基本信息。

\begin{table*}[htbp]
\renewcommand{\arraystretch}{1.2}
\caption{实验数据集详细信息}
\label{tab:datasets}
\centering
\scriptsize
\begin{tabular}{p{2.0cm}p{1.8cm}p{2.2cm}p{1.8cm}p{2.0cm}p{1.8cm}p{2.0cm}}
\toprule
\textbf{数据集} & \textbf{地理位置} & \textbf{主要树种} & \textbf{图像数量} & \textbf{数据来源} & \textbf{采集时间} & \textbf{主要特征} \\
\midrule
橡胶树数据集 & 中国海南 & \textit{Hevea} & 5,281 & UAV (80m) & 2023.11- & 极端物候 \\
(RT-Set) & 儋州 & \textit{brasiliensis} & (3697/ & Phantom 4 & 2024.07 & 变化，四个 \\
 & 19°31'N &  & 1056/528) & RTK &  & 完整周期 \\
\midrule
杨梅树数据集 & 中国浙江 & \textit{Myrica} & 2,430 & UAV & 2023.05- & 球形冠层 \\
(BT-Set) & 永嘉 & \textit{rubra} & (1701/ & DJI & 2024.04 & 结构，强 \\
 & 28°17'N &  & 486/243) & Phantom 4 &  & 阴影效应 \\
\midrule
城市树冠 & 西班牙 & 混合阔叶/ & 1,420 & 机载传感器 & 2022.06- & 复杂城市 \\
数据集 & 莱里达 & 针叶 & (994/ & 高分辨率 & 2023.09 & 背景，人工 \\
(UT-Set) & 41°37'N &  & 284/142) & 航拍 &  & 修剪形状 \\
\midrule
魁北克森林 & 加拿大 & \textit{Picea} & 5,321 & 公开数据集 & 2023.07- & 高密度针 \\
(CTF-Set) & 魁北克 & spp.混合 & (仅测试集) & 温带混 & 2024.06 & 叶林，24个 \\
 & 45°59'N & 落叶/针叶 &  & 交林 &  & 树种 \\
\bottomrule
\end{tabular}
\end{table*}

\textbf{橡胶树数据集（RT-Set）}：该数据集采集自中国海南省儋州市中国热带农业科学院橡胶研究所建立的实验林区，涵盖萌芽期、生长期、成熟期和落叶期四个完整物候周期。数据采集使用DJI Phantom 4 RTK无人机，配备1英寸CMOS传感器（2000万有效像素），飞行高度80米，横向和纵向重叠率均为85\%。该数据集的独特之处在于其极端的季节性变化特征，落叶期样本仅占训练集4.7\%，为评估模型在物候动态条件下的稳健性提供了理想的测试平台。

\textbf{杨梅树数据集（BT-Set）}：采集自中国浙江省永嘉县大洋山森林公园（28°17'N–28°19'N, 120°26'E–120°28'E），该区域属于中亚热带常绿阔叶林带。杨梅树具有独特的球形冠层结构和密集的分枝模式，但强阴影效应和密集的林下植被与树冠具有高度光谱相似性，使得该数据集在实例分割任务中极具挑战性。

\textbf{城市树冠数据集（UT-Set）}：来源于西班牙莱里达市（41°37'N, 0°37'E）的地中海城市环境，涵盖59公顷的异质城市基础设施。该数据集包含多种城市环境，如主要街道、住宅区、公园和密集建筑区，提供了广泛的场景多样性和结构遮挡。数据集包含14,772个标注树冠，其中许多被建筑物、车辆或街道设施部分遮挡。

\textbf{魁北克森林数据集（CTF-Set）}：采集自加拿大魁北克省圣伊波利特（45°59'N, 74°00'W）的温带混交林，研究区域跨越丘陵、湿地和湖泊等多样地形，支持典型的北美未管理森林中的多种落叶和针叶树种。该数据集的关键挑战在于冠层内的高物种多样性，包含十多个树种和属，具有独特但经常重叠的冠层结构。

表\ref{tab:phenology}进一步详细描述了RT-Set数据集中不同物候期的样本分布情况，展现了橡胶树极端季节性变化的数据特征。

\begin{table}[htbp]
\renewcommand{\arraystretch}{1.2}
\caption{橡胶树数据集物候期分布}
\label{tab:phenology}
\centering
\begin{tabular}{lccc}
\toprule
\textbf{物候期} & \textbf{训练集} & \textbf{验证集} & \textbf{测试集} \\
\midrule
萌芽期 & 1,074 & 271 & 131 \\
生长期 & 1,381 & 286 & 136 \\
成熟期 & 1,068 & 243 & 120 \\
落叶期 & 174 & 256 & 141 \\
\midrule
\textbf{总计} & \textbf{3,697} & \textbf{1,056} & \textbf{528} \\
\bottomrule
\end{tabular}
\end{table}

\section{Methodology}

\subsection{问题建模与总体架构}

\subsubsection{问题定义}
橡胶树冠分割任务可以形式化为一个多时相实例分割问题。给定多时相遥感图像序列$\mathcal{I} = \{I_1, I_2, \ldots, I_T\}$，其中$I_t \in \mathbb{R}^{H \times W \times 3}$表示第$t$个时相的RGB图像，目标是预测每个时相对应的实例分割掩膜$\mathcal{M} = \{M_1, M_2, \ldots, M_T\}$，其中$M_t \in \{0, 1, \ldots, N_t\}^{H \times W}$，$N_t$为第$t$时相的树冠实例数量。

橡胶树冠分割面临三个核心挑战：(1) \textbf{边界模糊性}：相邻树冠的枝叶交错重叠，导致边界$\partial \Omega_i$难以准确定位；(2) \textbf{时相变异性}：不同物候期的光谱特征$\phi(I_t)$存在显著差异，满足$\|\phi(I_i) - \phi(I_j)\|_2 > \tau$，其中$\tau$为变异阈值；(3) \textbf{背景干扰性}：非目标植被与橡胶树在光谱空间中存在重叠区域$\Phi_{rubber} \cap \Phi_{weed} \neq \emptyset$。

\subsubsection{CSAF框架设计}
针对上述挑战，本文提出基于物理约束状态空间模型的橡胶树冠分割框架（CSAF）。该框架采用"感知-推理-约束"的三层架构设计：

\textbf{感知层}：采用改进的ResNet-50骨干网络提取多尺度特征。为增强边界感知能力，在标准FPN基础上集成拉普拉斯金字塔算子$\mathcal{L}$：
\begin{equation}
F_{enhanced} = \mathcal{L}(F_{FPN}) = F_{FPN} - \text{Upsample}(\text{Gaussian}(F_{FPN}))
\end{equation}

生成增强特征金字塔$\{F_{p2}, F_{p3}, F_{p4}, F_{p5}, F_{p6}\}$，其中$F_{pi} \in \mathbb{R}^{B \times 256 \times H_i \times W_i}$。

\textbf{推理层}：包含三个专门化模块，分别解决三个核心挑战：
\begin{itemize}
\item GM-Mamba模块：通过状态空间模型建模长程边界依赖，解决边界模糊性
\item MASA-Optimizer模块：通过多智能体持续学习机制，解决时相变异性
\item MPC-Poisson模块：通过物理信息约束，解决背景干扰性
\end{itemize}

\textbf{约束层}：通过耦合数据驱动与物理约束优化引擎（CDPO-E）实现模块间协同。设$\theta_{GM}$、$\theta_{MASA}$、$\theta_{MPC}$分别为三个模块的参数，总体优化目标为：
\begin{equation}
\min_{\theta_{GM}, \theta_{MASA}, \theta_{MPC}} \mathcal{L}_{data}(\theta_{GM}) + \lambda_1 \mathcal{L}_{continual}(\theta_{MASA}) + \lambda_2 \mathcal{L}_{physics}(\theta_{MPC})
\end{equation}

其中$\lambda_1$、$\lambda_2$为平衡权重，通过MASA-Optimizer动态调整。

\subsection{基于状态空间模型的边界增强机制}

\subsubsection{边界模糊性问题分析}
橡胶树冠边界模糊性源于两个因素：(1) 相邻树冠的物理重叠，形成连续的冠层覆盖；(2) 传统卷积神经网络的局部感受野限制，难以捕获长程边界连续性。为量化边界模糊程度，定义边界清晰度指标：
\begin{equation}
\text{BC}(\partial \Omega) = \frac{1}{|\partial \Omega|} \sum_{p \in \partial \Omega} \|\nabla I(p)\|_2
\end{equation}

其中$\partial \Omega$为树冠边界，$|\partial \Omega|$为边界长度，$\nabla I(p)$为点$p$处的梯度。橡胶树冠的BC值通常低于0.3，显著低于建筑物等人工目标的0.8。

\subsubsection{GM-Mamba模块设计}
针对边界模糊性问题，设计GM-Mamba（Gradient-enhanced Mamba）模块，通过状态空间模型建模长程边界依赖关系。该模块基于以下观察：橡胶树叶片沿枝条方向呈现有序排列，形成具有方向性的边界模式。

\textbf{频域梯度增强策略}：传统空域梯度算子易受噪声干扰，本文采用频域处理策略。对输入特征$F \in \mathbb{R}^{B \times C \times H \times W}$进行2D-FFT变换：
\begin{equation}
\hat{F}(\omega_x, \omega_y) = \mathcal{F}\{F(x,y)\} = \int_{-\infty}^{\infty} \int_{-\infty}^{\infty} F(x,y) e^{-j2\pi(\omega_x x + \omega_y y)} dx dy
\end{equation}

在频域中，梯度操作等价于乘以频率因子：
\begin{equation}
\mathcal{F}\{\nabla F\} = j2\pi(\omega_x \hat{u}_x + \omega_y \hat{u}_y) \hat{F}(\omega_x, \omega_y)
\end{equation}

通过分别处理实部和虚部，获得多尺度梯度表示：
\begin{equation}
\begin{aligned}
G_{coarse} &= \mathcal{F}^{-1}\{j2\pi\omega_x \text{Re}(\hat{F})\} \\
G_{fine} &= \mathcal{F}^{-1}\{j2\pi\omega_y \text{Im}(\hat{F})\}
\end{aligned}
\end{equation}

\textbf{状态空间模型构建}：将增强后的特征重塑为序列$\mathbf{x} = \{x_1, x_2, \ldots, x_L\} \in \mathbb{R}^{L \times D}$，其中$L = H \times W$。构建线性时不变状态空间模型：
\begin{equation}
\begin{aligned}
h_{k+1} &= \mathbf{A} h_k + \mathbf{B} x_k \\
y_k &= \mathbf{C} h_k + \mathbf{D} x_k
\end{aligned}
\end{equation}

其中$\mathbf{A} \in \mathbb{R}^{N \times N}$为状态转移矩阵，$\mathbf{B} \in \mathbb{R}^{N \times D}$为输入矩阵，$\mathbf{C} \in \mathbb{R}^{D \times N}$为输出矩阵，$\mathbf{D} \in \mathbb{R}^{D \times D}$为前馈矩阵。

\textbf{选择性扫描机制}：为适应不同树冠的边界特征，引入选择性扫描机制。通过学习时变参数$\Delta_k$、$\mathbf{B}_k$、$\mathbf{C}_k$：
\begin{equation}
\Delta_k, \mathbf{B}_k, \mathbf{C}_k = \text{Linear}(x_k)
\end{equation}

离散化状态空间模型：
\begin{equation}
\begin{aligned}
\overline{\mathbf{A}}_k &= \exp(\Delta_k \mathbf{A}) \\
\overline{\mathbf{B}}_k &= (\Delta_k \mathbf{A})^{-1}(\overline{\mathbf{A}}_k - \mathbf{I}) \Delta_k \mathbf{B}_k
\end{aligned}
\end{equation}

最终输出通过递归计算获得：
\begin{equation}
\begin{aligned}
h_k &= \overline{\mathbf{A}}_k h_{k-1} + \overline{\mathbf{B}}_k x_k \\
y_k &= \mathbf{C}_k h_k + \mathbf{D} x_k
\end{aligned}
\end{equation}

该机制使模型能够自适应地关注不同空间位置的边界特征，有效建模橡胶树冠的长程边界依赖关系。

\subsection{基于物理信息的形态约束机制}

\subsubsection{背景干扰问题建模}
橡胶园中的非目标植被（如飞机草、薇甘菊等杂草）与橡胶树在光谱特征上存在相似性，导致严重的误分类。通过分析不同植被的形态学特征，发现关键差异：橡胶树冠呈现规则的近圆形或椭圆形结构，而杂草呈现不规则的分散分布。

定义形态规律性指标来量化这种差异：
\begin{equation}
\text{MR}(\Omega) = \frac{\text{Area}(\Omega)}{\text{Perimeter}(\Omega)^2} \times 4\pi
\end{equation}

其中$\Omega$为植被区域。橡胶树冠的MR值通常在0.6-0.9之间，而杂草的MR值低于0.3。

\subsubsection{物理信息神经网络设计}
基于扩散理论，树冠的空间分布遵循扩散过程的物理规律。本文构建MPC-Poisson（Morphology-constrained Physics-informed Poisson）模块，将物理先验嵌入神经网络。

\textbf{扩散方程建模}：将树冠存在概率场$u(x,y,t)$建模为扩散过程，满足扩散方程：
\begin{equation}
\frac{\partial u}{\partial t} = D \nabla^2 u + S(x,y)
\end{equation}

其中$D$为扩散系数，$S(x,y)$为源项。在稳态条件下（$\frac{\partial u}{\partial t} = 0$），方程简化为泊松方程：
\begin{equation}
\nabla^2 u(x,y) = -\frac{S(x,y)}{D}
\end{equation}

\textbf{神经网络参数化}：使用深度神经网络$\mathcal{N}_\theta$参数化概率场：
\begin{equation}
u(x,y) = \mathcal{N}_\theta(x,y)
\end{equation}

网络采用四层全连接结构：
\begin{equation}
\mathcal{N}_\theta: (x,y) \mapsto \text{FC}_{64} \mapsto \text{Swish} \mapsto \text{FC}_{32} \mapsto \text{Swish} \mapsto \text{FC}_{16} \mapsto \text{Swish} \mapsto \text{FC}_{1}
\end{equation}

选择Swish激活函数$\text{Swish}(x) = x \cdot \sigma(x)$以保证高阶导数的连续性。

\textbf{物理约束损失}：通过自动微分计算泊松方程残差：
\begin{equation}
\mathcal{R}_{PDE}(x,y) = \frac{\partial^2 u}{\partial x^2} + \frac{\partial^2 u}{\partial y^2} + \frac{S(x,y)}{D}
\end{equation}

物理约束损失定义为：
\begin{equation}
\mathcal{L}_{physics} = \frac{1}{N_{pde}} \sum_{i=1}^{N_{pde}} |\mathcal{R}_{PDE}(x_i, y_i)|^2
\end{equation}

其中$N_{pde}$为物理约束点数量。

\textbf{边界条件约束}：对于树冠边界$\partial \Omega$，施加Dirichlet边界条件：
\begin{equation}
u(x,y)|_{\partial \Omega} = u_{bc}(x,y)
\end{equation}

边界损失为：
\begin{equation}
\mathcal{L}_{boundary} = \frac{1}{N_{bc}} \sum_{i=1}^{N_{bc}} |u(x_i, y_i) - u_{bc}(x_i, y_i)|^2
\end{equation}

\textbf{总体损失函数}：结合数据拟合损失、物理约束损失和边界条件损失：
\begin{equation}
\mathcal{L}_{MPC} = \mathcal{L}_{data} + \lambda_{phy} \mathcal{L}_{physics} + \lambda_{bc} \mathcal{L}_{boundary}
\end{equation}

其中$\lambda_{phy} = 0.1$，$\lambda_{bc} = 0.001$为权重系数。

该机制通过物理先验有效约束分割结果的空间连贯性，显著抑制不规则杂草的误检测。

\subsection{基于多智能体的持续学习优化机制}

\subsubsection{时相变异性问题分析}
橡胶树在不同物候期表现出显著的光谱变异性，导致传统模型在跨时相应用时性能急剧下降。定义时相变异度量：
\begin{equation}
\text{TV}(t_i, t_j) = \frac{\|\mu_{t_i} - \mu_{t_j}\|_2}{\|\mu_{t_i}\|_2 + \|\mu_{t_j}\|_2}
\end{equation}

其中$\mu_{t_i}$为第$t_i$时相的特征均值。橡胶树在落叶期与成熟期的TV值可达0.85，远超一般植被的0.3。

这种变异性导致灾难性遗忘问题：模型在学习新时相特征时，会覆盖已学习的历史时相知识。设$\theta_t$为第$t$时相训练后的模型参数，灾难性遗忘程度定义为：
\begin{equation}
\text{CF}(t) = \frac{1}{t-1} \sum_{i=1}^{t-1} \frac{\mathcal{L}_i(\theta_t) - \mathcal{L}_i(\theta_i)}{\mathcal{L}_i(\theta_i)}
\end{equation}

\subsubsection{MASA-Optimizer设计原理}
针对时相变异性问题，设计MASA-Optimizer（Multi-Agent Simulated Annealing Optimizer），通过多智能体协作动态调节知识保留与遗忘的平衡。

\textbf{核心思想}：引入可学习的遗忘因子$\alpha \in [0,1]$，控制历史知识与新知识的融合比例：
\begin{equation}
\mathcal{L}_{continual} = \alpha \mathcal{L}_{new} + (1-\alpha) \mathcal{L}_{replay}
\end{equation}

其中$\mathcal{L}_{new}$为当前时相损失，$\mathcal{L}_{replay}$为历史时相回放损失。

\textbf{三阶段优化策略}：

\textit{阶段1：模拟退火探索}：在训练初期，使用模拟退火算法探索$\alpha$的全局最优解。定义能量函数：
\begin{equation}
E(\alpha) = \mathcal{L}_{continual}(\alpha) + \lambda_{reg} \|\alpha - \alpha_{prior}\|_2^2
\end{equation}

接受概率为：
\begin{equation}
P(\alpha_{new}) = \min\left(1, \exp\left(-\frac{E(\alpha_{new}) - E(\alpha_{old})}{T}\right)\right)
\end{equation}

温度按几何衰减：$T_{k+1} = \beta T_k$，其中$\beta = 0.95$。

\textit{阶段2：强化学习优化}：将$\alpha$的调整建模为马尔可夫决策过程。状态$s_t$为当前特征统计量，动作$a_t$为$\alpha$的调整量，奖励函数为：
\begin{equation}
r_t = -\mathcal{L}_{continual}(\alpha_t) - \lambda_{stability} \|\alpha_t - \alpha_{t-1}\|_2
\end{equation}

使用Deep Q-Network (DQN)学习最优策略：
\begin{equation}
Q^*(s,a) = \mathbb{E}[r_t + \gamma \max_{a'} Q^*(s_{t+1}, a') | s_t = s, a_t = a]
\end{equation}

\textit{阶段3：遗传算法精调}：在训练后期，使用遗传算法进行局部精调。种群大小为20，每个个体为一个$\alpha$值。适应度函数：
\begin{equation}
f(\alpha) = \frac{1}{1 + \mathcal{L}_{continual}(\alpha) + \lambda_{diversity} \text{Var}(\alpha)}
\end{equation}

交叉操作：$\alpha_{child} = w \alpha_{parent1} + (1-w) \alpha_{parent2}$，其中$w \sim \text{Beta}(2,2)$。

\textbf{经验回放机制}：维护经验缓冲区$\mathcal{B} = \{(x_i, y_i, t_i)\}_{i=1}^{N}$，存储历史时相样本。采用重要性采样策略：
\begin{equation}
p(x_i, y_i) \propto \exp\left(-\frac{\text{TV}(t_i, t_{current})}{\tau}\right)
\end{equation}

其中$\tau = 0.1$为温度参数，确保时相相似的样本有更高的采样概率。

\textbf{自适应融合策略}：最终的特征融合采用注意力机制：
\begin{equation}
\mathcal{F}_{final} = \sum_{t=1}^{T} w_t \mathcal{F}_t, \quad w_t = \frac{\exp(\alpha \cdot s_t)}{\sum_{j=1}^{T} \exp(\alpha \cdot s_j)}
\end{equation}

其中$s_t$为第$t$时相特征的重要性得分。

该机制通过多智能体协作，实现了跨物候期知识的有效保留与适应，显著提升了模型在极端时相变异条件下的稳定性。

\section{Experimental Setup}


\section*{Acknowledgment}

The authors would like to thank the anonymous reviewers for their valuable comments and suggestions.

% references section
\bibliographystyle{IEEEtran}
\bibliography{ieee_tgrs_references}

\begin{IEEEbiography}{Firstname Lastname}
received the B.S. degree in remote sensing from University A in 2015, and the Ph.D. degree in geoscience and remote sensing from University B in 2020. He is currently a Research Scientist with the Institute of Remote Sensing. His research interests include deep learning, plant phenotyping, and precision agriculture.
\end{IEEEbiography}

\begin{IEEEbiography}{Secondname Lastname}
received the Ph.D. degree in computer science from University C in 2018. She is currently an Associate Professor with the Department of Remote Sensing. Her research focuses on machine learning applications in agriculture and environmental monitoring.
\end{IEEEbiography}

\begin{IEEEbiography}{Thirdname Lastname}
is a Professor and Director of the Remote Sensing Laboratory. His research interests include hyperspectral remote sensing, crop monitoring, and precision agriculture technologies.
\end{IEEEbiography}

\end{document}
