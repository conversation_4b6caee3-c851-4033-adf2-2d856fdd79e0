\documentclass[journal]{IEEEtran}

% *** PACKAGES ***
\usepackage{cite}
\usepackage{graphicx}
\graphicspath{{fig/}}  % 设置图片搜索路径
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{array}
\usepackage{mdwmath}
\usepackage{mdwtab}
\usepackage{eqparbox}
\usepackage{url}
\usepackage{subfigure}
\usepackage{multirow}
\usepackage{booktabs}
\usepackage{color}
\usepackage{balance}
\usepackage{fontspec}
\usepackage{xeCJK}
% *** PDF, URL AND HYPERLINK PACKAGES ***
\usepackage[xetex,colorlinks=true,bookmarks=false]{hyperref}

% correct bad hyphenation here
\hyphenation{op-tical net-works semi-conduc-tor}

\begin{document}

% paper title
\title{基于物理约束状态空间模型的多时相遥感图像橡胶树冠高精度分割方法}

% author names and affiliations
\author{Firstname~Lastname,~\IEEEmembership{Member,~IEEE,}
        Secondname~Lastname,~\IEEEmembership{Senior~Member,~IEEE,}
        and~Thirdname~Lastname,~\IEEEmembership{Fellow,~IEEE}
\thanks{F. Lastname is with the Department of Remote Sensing, University of Agriculture, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{S. Lastname is with the Institute of Geoscience and Remote Sensing, Research Center, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{T. Lastname is with the Department of Computer Science, Technology University, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{Manuscript received Month DD, YYYY; revised Month DD, YYYY.}}

% make the title area
\maketitle

\begin{abstract}
高精度橡胶树冠分割是热带经济作物遥感监测的核心技术挑战。传统方法在处理复杂森林环境中的模糊边界、物种多样性和时相变化时存在显著局限。本文提出了一种融合物理约束的状态空间模型框架，专门用于多时相遥感图像的橡胶树冠精确分割。该框架包含三个核心创新：(1) GM-Mamba模块，首次将状态空间模型引入遥感图像分割，通过傅里叶域增强和选择性扫描机制实现长程空间依赖建模；(2) MPC-Poisson约束模块，基于扩散方程的物理先验抑制非目标植被干扰；(3) MASA-Optimizer，通过多智能体协作解决跨时相学习中的灾难性遗忘问题。在六个不同生态环境的遥感数据集上的实验表明，该方法在边界精度、形状保真度和时间一致性方面显著优于现有方法，AP50达到76.63\%，在落叶期样本仅占训练集4.7%的情况下仍保持91.16%的年度计数精度。该研究为遥感图像分割提供了新的理论框架，推动了物理信息神经网络在遥感领域的应用发展。
\end{abstract}

\begin{IEEEkeywords}
遥感；橡胶树冠分割；状态空间模型；物理信息神经网络；多时相分析；图像分割；森林遥感
\end{IEEEkeywords}

\IEEEpeerreviewmaketitle

\section{引言}
准确的单株树冠分割对于森林资源调查、生态系统评估和精准林业管理具有重要意义。橡胶种植园作为重要的经济林，其单株树冠的精确识别对于割胶计划制定、产量预测和病虫害防控等关键环节至关重要\cite{priyadarshan2017biology}。然而，橡胶树独特的生物学特性给遥感监测带来了特殊挑战。与天然森林相比，橡胶种植园虽然具有规则的行列式分布和相对一致的树龄，但其极端的季节性变化使得遥感图像解译变得异常困难。在高分辨率遥感影像中，橡胶树冠层在不同物候期呈现截然不同的特征：落叶期冠层覆盖度急剧下降至20\%以下，萌芽期新叶呈现红褐色反射，成熟期密集的枝叶交错形成复杂的阴影模式。这些剧烈的物候变化使得基于单一时相训练的模型在其他物候期性能显著下降，严重制约了橡胶林的自动化监测。

针对树冠分割任务，已有研究发展了两大类方法：传统方法和深度学习方法。根据Ke和Quackenbush\cite{ke2011review}的综述，传统树冠分割方法主要包括三种类型：谷线跟踪、区域生长和分水岭分割。谷线跟踪方法依赖森林内部的阴影间隙来分割树冠\cite{gougeon1998automatic}，在成熟针叶林中表现良好，但在自遮蔽树冠或复杂森林条件下精度下降。区域生长方法广泛用于树冠分割，通常利用树冠的光谱特征进行分割\cite{culvenor2002tida, erikson2003segmentation}。该算法需要种子点开始生长和停止生长的准则，但在复杂混交林中准确检测树顶和确定最优阈值都很困难。分水岭分割方法主要基于图像中的边缘信息，但图像噪声会导致过分割问题。

为改善传统分水岭分割的过分割问题，研究者提出了两类改进方法。第一类是多尺度分析方法。Jing等人\cite{jing2012automated}基于三个主要尺度的高斯滤波器平滑图像进行多尺度分水岭分割，然后从不同尺度的结果中选择最优分割。Yang等人\cite{yang2014automated}基于计算的梯度图像进行分水岭分割，采用多尺度阈值生成多尺度分割结果。第二类是标记控制分水岭分割，其中标记是基于局部最大值检测的树顶。Wang等人\cite{wang2004automated, wang2010crown}检测了两种不同类型的树顶：光谱局部最大值和空间局部最大值。Lamar等人\cite{lamar2005automated}和Tong等人\cite{tong2021improved}利用空间局部最大值检测树顶。

近年来，深度学习方法被广泛应用于树冠分割任务\cite{zhao2023review}。一类深度学习方法将深度学习与分水岭分割相结合。例如，Lassalle等人\cite{lassalle2022cnn}利用CNN模型计算距离图，指示像素到最近树冠边界的距离，然后通过在距离图中定位局部最大值来识别树顶。Freudenberg等人\cite{freudenberg2022individual}利用U-Net\cite{ronneberger2015u}预测树冠掩膜、树冠轮廓和距离图。另一类深度学习方法采用实例分割技术，其中Mask R-CNN\cite{he2017mask}是树冠分割研究中使用最广泛的深度学习模型\cite{braga2020amazon, hao2021individual, ball2023detectree2}。

尽管深度学习方法通常优于传统方法，但在橡胶树冠分割中仍面临三个关键挑战。首先，树冠轮廓不清晰问题。成熟橡胶树冠幅可达8-12米，相邻树木的枝叶经常交错重叠，形成连续的冠层覆盖，单株树木的边界变得极其模糊。传统的边缘检测算法难以准确提取单株树冠边界，深度学习模型也容易产生欠分割现象。其次，物候动态引发的特征漂移问题。橡胶树在落叶期、萌芽期、生长期和成熟期表现出截然不同的光谱反射特征，多时相NDVI曲线呈现非连续性变化，使得基于单一时相训练的模型在其他物候期性能急剧下降。第三，非目标植被的干扰问题。橡胶园常见的飞机草、薇甘菊等杂草在近红外波段的反射特征与橡胶幼树高度相似，形状相似性使得杂草的叶片形态与橡胶幼树相近，传统的光谱分析方法无法有效区分，导致严重的误分类现象。

针对上述挑战，本文提出了一种基于物理约束状态空间模型的橡胶树冠分割框架（CSAF）。该框架专门设计来解决橡胶树遥感监测中的三个核心问题：1）针对树冠轮廓不清晰问题，我们设计了基于状态空间模型的边界增强模块（GM-Mamba），首次将状态空间模型引入树冠分割任务，通过傅里叶域增强和选择性扫描机制建模树冠边界的长程空间依赖关系，有效捕获橡胶树叶片沿枝条方向的连续性特征；2）针对物候动态引发的特征漂移问题，我们开发了多智能体持续学习机制（MASA-Optimizer），通过自适应记忆管理和经验回放避免跨物候期训练中的灾难性遗忘，确保模型在极端物候变化下的稳定性；3）针对非目标植被干扰问题，我们构建了基于泊松方程的物理约束模块（MPC-Poisson），利用扩散过程的形态学先验约束分割结果的空间连贯性和结构合理性，有效抑制形状相似、纹理相近、分布模式复杂的杂草干扰。



为评估所提出方法的性能，我们在构建的跨物候期橡胶树数据集上进行了全面实验，该数据集涵盖萌芽期、生长期、成熟期和落叶期四个完整阶段。与广泛认可的基准模型相比，我们的CSAF框架在RT-Set数据集上实现了AP50为76.63\%的性能，相比基线模型提升了6.11个百分点。特别值得注意的是，在落叶期样本仅占训练集4.7\%的极端不平衡情况下，CSAF在年度计数任务中仍保持91.16\%的平均精度，显著优于对比方法的63.75\%。此外，我们还分析了超参数设置和图像空间分辨率对分割精度的影响，为未来用户在分割任务中应用所提出模型提供指导。

本文的其余部分组织如下：第2节提供了研究区域描述、所提出树冠分割方法的详细信息以及树冠分割评估指标的介绍。第3节展示了实验结果，包括分割结果描述、分割误差分析以及与其他方法的比较。第4节分析了所提出分割方法的参数设置，分析了空间分辨率的影响，探讨了森林研究的前景，并提出了可能的进一步改进。第5节为研究提供了总体结论。





\section{材料与方法}

\subsection{研究区域}
研究区域位于中国海南省儋州市西北部，地理坐标为19°31'50.59''N，109°28'52.62''E，隶属于中国热带农业科学院橡胶研究所建立的实验林区。该区域属于南亚热带季风气候区，气候特征为全年温暖，年内温差较小。年平均气温介于22.5°C至25.6°C之间，其中1月为最冷月，7月为最热月。年日照时数为1780-2600小时，年降水量在900-2400毫米之间，为橡胶树生长提供了良好的水热条件\cite{priyadarshan2017biology}。

值得注意的是，海南岛位于南海北缘，是经常受台风影响的沿海岛屿之一。主要台风季节为6月至10月。虽然儋州位于岛屿西北部，远离典型的台风登陆区，但仍经常受到热带气旋及其外围云系的影响。这些扰动包括极端风力和强降雨，常常造成树木倒伏和冠层破碎等结构性损害。作为典型的热带经济作物，橡胶树的冠层结构对风力扰动高度敏感。因此，在该区域进行野外观测和遥感研究，不仅能够收集不同扰动强度下的冠层响应数据，还为评估所提出的树冠分割和生态监测框架在复杂背景条件下的稳健性和泛化能力提供了理想的测试平台。此外，该区域为评估框架在极端天气情景下的适应性和监测精度提供了合适的环境，在灾害响应和森林健康评估方面具有巨大的应用潜力。研究区域的具体位置如图\ref{fig:study_area}所示。

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\columnwidth]{Study Area.pdf}
    \caption{研究区域位置图。}
    \label{fig:study_area}
\end{figure}

\subsection{数据集构建}
为了全面评估所提出方法的性能和泛化能力，本研究在四个涵盖不同地理环境和树种的数据集上进行了广泛实验，包括热带橡胶种植园、温带果树园、城市绿化带和北方针叶林等多样化生态系统。表\ref{tab:datasets}详细描述了各数据集的基本信息。

\begin{table*}[htbp]
\renewcommand{\arraystretch}{1.2}
\caption{实验数据集详细信息}
\label{tab:datasets}
\centering
\scriptsize
\begin{tabular}{p{2.0cm}p{1.8cm}p{2.2cm}p{1.8cm}p{2.0cm}p{1.8cm}p{2.0cm}}
\toprule
\textbf{数据集} & \textbf{地理位置} & \textbf{主要树种} & \textbf{图像数量} & \textbf{数据来源} & \textbf{采集时间} & \textbf{主要特征} \\
\midrule
橡胶树数据集 & 中国海南 & \textit{Hevea} & 5,281 & UAV (80m) & 2023.11- & 极端物候 \\
(RT-Set) & 儋州 & \textit{brasiliensis} & (3697/ & Phantom 4 & 2024.07 & 变化，四个 \\
 & 19°31'N &  & 1056/528) & RTK &  & 完整周期 \\
\midrule
杨梅树数据集 & 中国浙江 & \textit{Myrica} & 2,430 & UAV & 2023.05- & 球形冠层 \\
(BT-Set) & 永嘉 & \textit{rubra} & (1701/ & DJI & 2024.04 & 结构，强 \\
 & 28°17'N &  & 486/243) & Phantom 4 &  & 阴影效应 \\
\midrule
城市树冠 & 西班牙 & 混合阔叶/ & 1,420 & 机载传感器 & 2022.06- & 复杂城市 \\
数据集 & 莱里达 & 针叶 & (994/ & 高分辨率 & 2023.09 & 背景，人工 \\
(UT-Set) & 41°37'N &  & 284/142) & 航拍 &  & 修剪形状 \\
\midrule
魁北克森林 & 加拿大 & \textit{Picea} & 5,321 & 公开数据集 & 2023.07- & 高密度针 \\
(CTF-Set) & 魁北克 & spp.混合 & (仅测试集) & 温带混 & 2024.06 & 叶林，24个 \\
 & 45°59'N & 落叶/针叶 &  & 交林 &  & 树种 \\
\bottomrule
\end{tabular}
\end{table*}

\textbf{橡胶树数据集（RT-Set）}：该数据集采集自中国海南省儋州市中国热带农业科学院橡胶研究所建立的实验林区，涵盖萌芽期、生长期、成熟期和落叶期四个完整物候周期。数据采集使用DJI Phantom 4 RTK无人机，配备1英寸CMOS传感器（2000万有效像素），飞行高度80米，横向和纵向重叠率均为85\%。该数据集的独特之处在于其极端的季节性变化特征，落叶期样本仅占训练集4.7\%，为评估模型在物候动态条件下的稳健性提供了理想的测试平台。

\textbf{杨梅树数据集（BT-Set）}：采集自中国浙江省永嘉县大洋山森林公园（28°17'N–28°19'N, 120°26'E–120°28'E），该区域属于中亚热带常绿阔叶林带。杨梅树具有独特的球形冠层结构和密集的分枝模式，但强阴影效应和密集的林下植被与树冠具有高度光谱相似性，使得该数据集在实例分割任务中极具挑战性。

\textbf{城市树冠数据集（UT-Set）}：来源于西班牙莱里达市（41°37'N, 0°37'E）的地中海城市环境，涵盖59公顷的异质城市基础设施。该数据集包含多种城市环境，如主要街道、住宅区、公园和密集建筑区，提供了广泛的场景多样性和结构遮挡。数据集包含14,772个标注树冠，其中许多被建筑物、车辆或街道设施部分遮挡。

\textbf{魁北克森林数据集（CTF-Set）}：采集自加拿大魁北克省圣伊波利特（45°59'N, 74°00'W）的温带混交林，研究区域跨越丘陵、湿地和湖泊等多样地形，支持典型的北美未管理森林中的多种落叶和针叶树种。该数据集的关键挑战在于冠层内的高物种多样性，包含十多个树种和属，具有独特但经常重叠的冠层结构。

表\ref{tab:phenology}进一步详细描述了RT-Set数据集中不同物候期的样本分布情况，展现了橡胶树极端季节性变化的数据特征。

\begin{table}[htbp]
\renewcommand{\arraystretch}{1.2}
\caption{橡胶树数据集物候期分布}
\label{tab:phenology}
\centering
\begin{tabular}{lccc}
\toprule
\textbf{物候期} & \textbf{训练集} & \textbf{验证集} & \textbf{测试集} \\
\midrule
萌芽期 & 1,074 & 271 & 131 \\
生长期 & 1,381 & 286 & 136 \\
成熟期 & 1,068 & 243 & 120 \\
落叶期 & 174 & 256 & 141 \\
\midrule
\textbf{总计} & \textbf{3,697} & \textbf{1,056} & \textbf{528} \\
\bottomrule
\end{tabular}
\end{table}

\section{Methodology}

\subsection{CSAF框架总体设计}

CSAF框架采用模块化架构设计，由特征提取骨干网络和三个专门化处理模块组成。骨干网络基于改进的ResNet-50实现，通过集成拉普拉斯变换和残差块构建多尺度特征金字塔。具体实现中，输入图像$I \in \mathbb{R}^{B \times 3 \times H \times W}$首先经过拉普拉斯变换模块进行边缘增强处理。该模块在频域中应用3×3和5×5拉普拉斯算子，通过二维快速傅里叶变换将输入转换为复数表示$\hat{I} = \text{FFT2D}(I)$，分别对实部和虚部应用拉普拉斯卷积核：

\begin{equation}
\begin{aligned}
L_{3 \times 3} &= \begin{bmatrix} 1 & 0 & 1 \\ 0 & -4 & 0 \\ 1 & 0 & 1 \end{bmatrix}, \quad L_{5 \times 5} = \begin{bmatrix} -2 & -4 & -4 & -4 & -2 \\ -4 & 0 & 8 & 0 & -4 \\ -4 & 8 & 24 & 8 & -4 \\ -4 & 0 & 8 & 0 & -4 \\ -2 & -4 & -4 & -4 & -2 \end{bmatrix}
\end{aligned}
\end{equation}

经过拉普拉斯变换后，实部和虚部特征在通道维度拼接形成$F_{lap} \in \mathbb{R}^{B \times 6 \times H \times W}$的增强特征表示。随后通过五个残差块构建特征金字塔$\{F_{p2}, F_{p3}, F_{p4}, F_{p5}, F_{p6}\}$，其中每个残差块包含两个3×3卷积层、批归一化和ReLU激活函数，输出通道数统一为256。各层特征的空间分辨率分别为原图的1/4、1/8、1/16、1/32和1/64。

三个专门化模块并行处理特征金字塔的输出。GM-Mamba模块针对每个尺度的特征$F_{pi} \in \mathbb{R}^{B \times 256 \times H_i \times W_i}$，首先通过einops库的rearrange函数将空间维度重塑为序列形式$F_{seq} \in \mathbb{R}^{B \times (H_i \times W_i) \times 256}$，然后输入到配置为d\_model=256、n\_layer=1、d\_state=4、dt\_rank=4的Mamba模块进行长程依赖建模。MASA-Optimizer模块维护一个容量为100的MemoryBuffer，存储历史时相的特征表示，通过iCaRLNet网络动态调整遗忘因子$\alpha$。MPC-Poisson模块采用四层多层感知机网络，输入归一化坐标$(x,y) \in [-1,1]^2$，输出树冠存在概率$u(x,y) \in [0,1]$，通过自动微分计算泊松方程残差$\nabla^2 u + f(x,y) = 0$实现物理约束。

框架的前向传播过程如下：给定输入图像$I$，首先通过拉普拉斯变换和残差块提取特征金字塔$\{F_{p2}, F_{p3}, F_{p4}, F_{p5}, F_{p6}\}$；然后GM-Mamba模块对每个尺度特征进行序列建模，输出增强的边界特征$\{F'_{p2}, F'_{p3}, F'_{p4}, F'_{p5}, F'_{p6}\}$；MASA-Optimizer根据当前特征统计量和历史经验计算遗忘因子$\alpha_t$；MPC-Poisson模块生成物理约束权重$W_{physics} \in \mathbb{R}^{B \times 1 \times H \times W}$；最终通过加权融合生成分割结果$S = \alpha_t \cdot F'_{enhanced} + (1-\alpha_t) \cdot F_{replay} \odot W_{physics}$，其中$F_{enhanced}$为GM-Mamba增强后的特征，$F_{replay}$为历史特征回放，$\odot$表示逐元素乘法。

\subsection{GM-Mamba边界增强模块}

GM-Mamba模块通过集成拉普拉斯变换和状态空间模型实现长程边界依赖建模。该模块的核心设计基于LaplacianTransform类和Mamba类的协同工作，其中LaplacianTransform负责边缘特征增强，Mamba负责序列依赖建模。

拉普拉斯变换组件采用双核设计，定义3×3和5×5两种拉普拉斯算子。3×3核定义为$L_{3 \times 3} = \begin{bmatrix} 0 & 1 & 0 \\ 1 & -4 & 1 \\ 0 & 1 & 0 \end{bmatrix}$，5×5核定义为$L_{5 \times 5} = \begin{bmatrix} 0 & 0 & 1 & 0 & 0 \\ 0 & 1 & 2 & 1 & 0 \\ 1 & 2 & -16 & 2 & 1 \\ 0 & 1 & 2 & 1 & 0 \\ 0 & 0 & 1 & 0 & 0 \end{bmatrix}$。这两个核通过register\_buffer注册为模型参数，确保在GPU上的高效计算。前向传播过程中，输入特征$x \in \mathbb{R}^{B \times 3 \times H \times W}$首先转换为复数张量$x_{complex} = x + 0j$，然后分离实部和虚部分别进行卷积操作。具体地，使用F.conv2d函数以groups=in\_channels的方式对实部和虚部分别应用两种拉普拉斯核，得到四个卷积结果：laplacian\_3x3\_real、laplacian\_5x5\_real、laplacian\_3x3\_imag和laplacian\_5x5\_imag。随后将同类型的结果相加合并：laplacian\_real = laplacian\_3x3\_real + laplacian\_5x5\_real，laplacian\_imag = laplacian\_3x3\_imag + laplacian\_5x5\_imag。最终通过torch.cat在通道维度拼接实部和虚部，输出维度为$\mathbb{R}^{B \times 6 \times H \times W}$的增强特征。

特征金字塔构建通过五个卷积层实现，每层采用3×3卷积核、步长为2、填充为1的配置。第一层将6通道的拉普拉斯输出转换为256通道，后续四层保持256通道不变，逐步降采样生成p2、p3、p4、p5、p6五个尺度的特征图，空间分辨率分别为原图的1/4、1/8、1/16、1/32、1/64。每个特征图的维度为$F_{pi} \in \mathbb{R}^{B \times 256 \times H_i \times W_i}$，其中$H_i = H/2^{i+1}$，$W_i = W/2^{i+1}$。

Mamba状态空间模型的配置通过ModelArgs数据类定义：d\_model=256对应特征维度，n\_layer=1表示单层结构，d\_state=4为状态维度，dt\_rank=4为时间步长参数的秩，expand=2为内部维度扩展因子，d\_conv=4为一维卷积核大小。每个尺度的特征图都配备独立的Mamba实例（mamba\_p2至mamba\_p6），确保不同尺度特征的独立处理。在前向传播中，空间特征通过einops.rearrange函数重塑为序列格式：$F_{seq} = \text{rearrange}(F_{pi}, 'b c h w -> b (h w) c')$，将空间维度$(h, w)$展平为序列长度$L = h \times w$，得到$F_{seq} \in \mathbb{R}^{B \times L \times 256}$的序列表示。

Mamba模块的核心是选择性扫描机制，通过MambaBlock实现。输入序列首先经过in\_proj线性层投影为$2 \times d_{inner}$维度，其中$d_{inner} = expand \times d_{model} = 2 \times 256 = 512$。投影结果分割为主分支$x$和残差分支$res$，主分支经过一维卷积conv1d和SiLU激活后输入SSM模块。SSM模块通过x\_proj线性层生成时变参数：$(\Delta, B, C) = \text{split}(\text{x\_proj}(x))$，其中$\Delta \in \mathbb{R}^{B \times L \times dt\_rank}$为时间步长参数，$B, C \in \mathbb{R}^{B \times L \times d\_state}$为输入输出矩阵。状态转移矩阵$A \in \mathbb{R}^{d_{inner} \times d\_state}$通过A\_log参数化为$A = -\exp(A_{log})$，确保系统稳定性。选择性扫描算法通过离散化状态空间方程实现：$\bar{A} = \exp(\Delta A)$，$\bar{B} = (\Delta A)^{-1}(\bar{A} - I)\Delta B$，最终输出$y = \text{selective\_scan}(x, \Delta, A, B, C, D)$。

模块输出通过残差连接和归一化处理。SSM输出与残差分支相乘后经过out\_proj线性层，再与输入进行残差连接，最后通过RMSNorm归一化得到最终的序列输出。该输出重新reshape回空间维度，与原始特征图进行融合，实现边界特征的增强。整个GM-Mamba模块的计算复杂度为$O(BL \cdot d_{model})$，相比自注意力机制的$O(BL^2 \cdot d_{model})$复杂度显著降低，使得模型能够高效处理高分辨率遥感图像中的长程边界依赖关系。

\subsection{MPC-Poisson物理约束模块}

MPC-Poisson模块的设计基于这样的物理观察：橡胶树冠的空间分布遵循扩散过程的物理规律，呈现出规则的近圆形或椭圆形形态，而杂草等非目标植被则表现为不规则的分散分布。这种形态差异为区分橡胶树冠和背景干扰提供了重要线索。传统的深度学习方法主要依赖数据驱动的特征学习，缺乏对物理规律的显式约束，容易在光谱相似的情况下产生误分类。因此，本文引入物理信息神经网络的思想，将扩散方程的物理约束嵌入到神经网络中，通过约束分割结果的空间连贯性和形态合理性来抑制背景干扰。

该模块将树冠存在概率场建模为稳态扩散过程，满足泊松方程$\nabla^2 u(x,y) = f(x,y)$，其中$u(x,y)$表示位置$(x,y)$处的树冠存在概率，$f(x,y)$为源项函数。在实际实现中，使用四层多层感知机网络来参数化概率场函数$u(x,y) = \mathcal{N}_\theta(x,y)$，网络采用Swish激活函数以保证高阶导数的连续性，输出层使用Sigmoid函数将结果约束在$[0,1]$区间内。网络的输入为归一化的空间坐标$(x,y) \in [-1,1]^2$，输出为对应位置的树冠存在概率。

物理约束通过自动微分技术实现。对于网络输出$u(x,y)$，计算其二阶偏导数$u_{xx} = \frac{\partial^2 u}{\partial x^2}$和$u_{yy} = \frac{\partial^2 u}{\partial y^2}$，然后构建泊松方程残差$\mathcal{R}_{Poisson}(x,y) = u_{xx} + u_{yy} + f(x,y)$。源项函数$f(x,y)$设计为高斯函数形式，模拟树冠从中心向外扩散的生长模式。物理约束损失定义为泊松方程残差的均方误差，通过最小化该损失来确保网络输出满足物理规律。

为了增强约束的有效性，模块采用多尺度物理约束策略。在不同的空间分辨率下分别施加泊松方程约束，并通过加权求和的方式组合不同尺度的约束损失。这种多尺度约束不仅能够捕获不同尺度的形态特征，还能提高约束的鲁棒性。同时，对于检测到的树冠边界区域，施加Dirichlet边界条件，进一步约束边界处的概率值。

MPC-Poisson模块与主分割网络的集成通过注意力机制实现。模块输出的概率场经过双线性插值调整到与特征图相同的空间分辨率，然后作为空间注意力权重与原始特征相乘，从而抑制不符合物理约束的区域。整个模块的训练采用联合优化的方式，将物理约束损失、边界条件损失和数据拟合损失相结合，通过梯度反传同时优化网络参数和物理约束的满足程度。这种物理信息神经网络的设计有效地将领域知识融入到深度学习模型中，显著提升了模型对背景干扰的抑制能力。

\subsection{MASA-Optimizer持续学习机制}

MASA-Optimizer模块专门用于解决跨物候期训练中的灾难性遗忘问题。橡胶树在不同物候期表现出极端的光谱变异性，传统的序贯学习方法在学习新时相特征时往往会覆盖已学习的历史时相知识，导致模型在之前时相上的性能急剧下降。为了解决这一问题，本文设计了基于多智能体协作的自适应遗忘因子优化机制，通过动态调节新旧知识的融合比例来实现持续学习。

该机制的核心思想是引入可学习的遗忘因子$\alpha \in [0,1]$来控制历史知识与新知识的平衡。具体地，将持续学习的损失函数定义为$\mathcal{L}_{continual} = \alpha \mathcal{L}_{new} + (1-\alpha) \mathcal{L}_{replay}$，其中$\mathcal{L}_{new}$为当前时相的损失，$\mathcal{L}_{replay}$为从经验缓冲区采样的历史时相回放损失。遗忘因子$\alpha$的值决定了模型对新旧知识的重视程度：当$\alpha$接近1时，模型主要关注新知识的学习；当$\alpha$接近0时，模型主要保持历史知识。关键在于如何动态地确定最优的$\alpha$值，使得模型既能有效学习新时相的特征，又能保持对历史时相的记忆。

MASA-Optimizer采用三阶段优化策略来搜索最优的遗忘因子。第一阶段使用模拟退火算法进行全局探索，通过随机扰动和概率接受机制来避免陷入局部最优解。具体地，在每次迭代中对当前的$\alpha$值添加高斯噪声生成候选解$\alpha_{new}$，然后根据能量变化$\Delta E = \mathcal{L}_{continual}(\alpha_{new}) - \mathcal{L}_{continual}(\alpha_{old})$和当前温度$T$计算接受概率$P_{accept} = \min(1, \exp(-\Delta E / T))$。温度按几何级数衰减，确保算法从高温的全局搜索逐渐过渡到低温的局部精化。第二阶段将$\alpha$的优化建模为马尔可夫决策过程，使用深度Q网络来学习最优的调整策略。状态空间包括当前特征的统计量和损失值，动作空间为$\alpha$的调整量，奖励函数设计为负的持续学习损失加上稳定性惩罚项。第三阶段使用遗传算法进行局部精调，通过种群进化的方式在前两阶段确定的优良区域内搜索最优解。

\begin{algorithm}[htbp]
\caption{MASA-Optimizer三阶段优化算法}
\label{alg:masa}
\begin{algorithmic}[1]
\REQUIRE 多时相数据集$\mathcal{D} = \{D_1, D_2, \ldots, D_T\}$，初始遗忘因子$\alpha_0 = 0.5$
\ENSURE 优化后的遗忘因子序列$\{\alpha_1, \alpha_2, \ldots, \alpha_T\}$
\STATE 初始化经验缓冲区$\mathcal{B} \leftarrow \emptyset$，设置阶段划分点$t_1 = 0.3T$，$t_2 = 0.7T$
\FOR{$t = 1$ \TO $T$}
    \IF{$t \leq t_1$} \STATE // 阶段1：模拟退火全局搜索
        \STATE $\alpha_t \leftarrow \text{SimulatedAnnealing}(\alpha_{t-1}, D_t, \mathcal{B})$
        \STATE 温度更新：$T_k \leftarrow T_0 \cdot 0.95^k$
    \ELSIF{$t \leq t_2$} \STATE // 阶段2：强化学习策略优化
        \STATE 构建状态：$s_t \leftarrow [\mu_{feat}, \sigma_{feat}, \mathcal{L}_{current}, \mathcal{L}_{replay}]$
        \STATE 选择动作：$a_t \leftarrow \epsilon\text{-greedy}(Q_\theta(s_t, \cdot))$
        \STATE 更新遗忘因子：$\alpha_t \leftarrow \text{clip}(\alpha_{t-1} + a_t, 0, 1)$
        \STATE 计算奖励：$r_t \leftarrow -\mathcal{L}_{continual}(\alpha_t) - \lambda_{stab}|\alpha_t - \alpha_{t-1}|$
        \STATE 更新Q网络：$\theta \leftarrow \theta - \nabla_\theta \mathcal{L}_{DQN}$
    \ELSE \STATE // 阶段3：遗传算法局部精调
        \STATE 初始化种群：$P \leftarrow \{\alpha_1, \ldots, \alpha_{20}\}$，$\alpha_i \sim \mathcal{N}(\alpha_{best}, 0.05^2)$
        \FOR{$g = 1$ \TO $G_{max}$}
            \STATE 评估适应度：$f_i \leftarrow 1/(1 + \mathcal{L}_{continual}(\alpha_i))$
            \STATE 锦标赛选择：$P_{select} \leftarrow \text{TournamentSelect}(P, k=3)$
            \STATE 算术交叉：$\alpha_{child} \leftarrow w\alpha_{p1} + (1-w)\alpha_{p2}$
            \STATE 高斯变异：$\alpha_{mutated} \leftarrow \alpha + \mathcal{N}(0, 0.02^2)$
            \STATE 更新种群：$P \leftarrow P_{select} \cup \{\alpha_{child}, \alpha_{mutated}\}$
        \ENDFOR
        \STATE $\alpha_t \leftarrow \arg\max_{\alpha \in P} f(\alpha)$
    \ENDIF
    \STATE 使用$\alpha_t$训练模型：$\theta \leftarrow \theta - \nabla_\theta[\alpha_t \mathcal{L}_{new} + (1-\alpha_t) \mathcal{L}_{replay}]$
    \STATE 更新经验缓冲区：$\mathcal{B} \leftarrow \mathcal{B} \cup \text{RandomSample}(D_t, n_{sample})$
\ENDFOR
\end{algorithmic}
\end{algorithm}

经验回放机制是MASA-Optimizer的重要组成部分。模块维护一个固定大小的经验缓冲区来存储历史时相的样本，并采用时相感知的重要性采样策略来选择回放样本。具体地，对于缓冲区中的每个样本$(x_i, y_i, t_i)$，其被采样的概率与当前时相$t_{current}$和样本时相$t_i$之间的相似性成正比，即$p(x_i, y_i, t_i) \propto \exp(-\beta \cdot \text{TV}(t_i, t_{current}))$，其中$\text{TV}(\cdot, \cdot)$为时相变异度量，$\beta$为温度参数。这种采样策略确保了与当前时相相似的历史样本有更高的被选中概率，从而提高回放的有效性。在每个训练批次中，新数据和回放数据按照预设比例混合，通过联合优化来实现新旧知识的平衡学习。整个MASA-Optimizer机制通过自适应地调节遗忘因子，有效地解决了跨物候期训练中的灾难性遗忘问题，显著提升了模型在多时相数据上的泛化能力。

\section{Experimental Setup}


\section*{Acknowledgment}

The authors would like to thank the anonymous reviewers for their valuable comments and suggestions.

% references section
\bibliographystyle{IEEEtran}
\bibliography{ieee_tgrs_references}

\begin{IEEEbiography}{Firstname Lastname}
received the B.S. degree in remote sensing from University A in 2015, and the Ph.D. degree in geoscience and remote sensing from University B in 2020. He is currently a Research Scientist with the Institute of Remote Sensing. His research interests include deep learning, plant phenotyping, and precision agriculture.
\end{IEEEbiography}

\begin{IEEEbiography}{Secondname Lastname}
received the Ph.D. degree in computer science from University C in 2018. She is currently an Associate Professor with the Department of Remote Sensing. Her research focuses on machine learning applications in agriculture and environmental monitoring.
\end{IEEEbiography}

\begin{IEEEbiography}{Thirdname Lastname}
is a Professor and Director of the Remote Sensing Laboratory. His research interests include hyperspectral remote sensing, crop monitoring, and precision agriculture technologies.
\end{IEEEbiography}

\end{document}
