\documentclass[journal]{IEEEtran}

% *** PACKAGES ***
% *** PACKAGES ***
\usepackage{cite}
\usepackage{graphicx}
\graphicspath{{fig/}}  % 设置图片搜索路径
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{algorithm}        % ✅ 保留 algorithm
\usepackage{algpseudocode}    % ✅ 替代 algorithmic，支持现代伪代码语法
\usepackage{array}
\usepackage{mdwmath}
\usepackage{mdwtab}
\usepackage{eqparbox}
\usepackage{url}
\usepackage{subfigure}
\usepackage{multirow}
\usepackage{booktabs}
\usepackage{color}
\usepackage{balance}
\usepackage{fontspec}
\usepackage{xeCJK}
% *** PDF, URL AND HYPERLINK PACKAGES ***
\usepackage[xetex,colorlinks=true,bookmarks=false]{hyperref}

% correct bad hyphenation here
\hyphenation{op-tical net-works semi-conduc-tor}

\begin{document}

% paper title
\title{基于物理约束状态空间模型的多时相遥感图像橡胶树冠高精度分割方法}

% author names and affiliations
\author{Firstname~Lastname,~\IEEEmembership{Member,~IEEE,}
        Secondname~Lastname,~\IEEEmembership{Senior~Member,~IEEE,}
        and~Thirdname~Lastname,~\IEEEmembership{Fellow,~IEEE}
\thanks{F. Lastname is with the Department of Remote Sensing, University of Agriculture, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{S. Lastname is with the Institute of Geoscience and Remote Sensing, Research Center, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{T. Lastname is with the Department of Computer Science, Technology University, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{Manuscript received Month DD, YYYY; revised Month DD, YYYY.}}

% make the title area
\maketitle

\begin{abstract}
高精度橡胶树冠分割是热带经济作物遥感监测的核心技术挑战。传统方法在处理复杂森林环境中的模糊边界、物种多样性和时相变化时存在显著局限。本文提出了一种融合物理约束的状态空间模型框架，专门用于多时相遥感图像的橡胶树冠精确分割。该框架包含三个核心创新：(1) GM-Mamba模块，首次将状态空间模型引入遥感图像分割，通过傅里叶域增强和选择性扫描机制实现长程空间依赖建模；(2) MPC-Poisson约束模块，基于扩散方程的物理先验抑制非目标植被干扰；(3) MASA-Optimizer，通过多智能体协作解决跨时相学习中的灾难性遗忘问题。在六个不同生态环境的遥感数据集上的实验表明，该方法在边界精度、形状保真度和时间一致性方面显著优于现有方法，AP50达到76.63\%，在落叶期样本仅占训练集4.7%的情况下仍保持91.16%的年度计数精度。该研究为遥感图像分割提供了新的理论框架，推动了物理信息神经网络在遥感领域的应用发展。
\end{abstract}

\begin{IEEEkeywords}
遥感；橡胶树冠分割；状态空间模型；物理信息神经网络；多时相分析；图像分割；森林遥感
\end{IEEEkeywords}

\IEEEpeerreviewmaketitle

\section{引言}
准确的单株树冠分割对于森林资源调查、生态系统评估和精准林业管理具有重要意义。橡胶种植园作为重要的经济林，其单株树冠的精确识别对于割胶计划制定、产量预测和病虫害防控等关键环节至关重要\cite{priyadarshan2017biology}。然而，橡胶树独特的生物学特性给遥感监测带来了特殊挑战。与天然森林相比，橡胶种植园虽然具有规则的行列式分布和相对一致的树龄，但其极端的季节性变化使得遥感图像解译变得异常困难。在高分辨率遥感影像中，橡胶树冠层在不同物候期呈现截然不同的特征：落叶期冠层覆盖度急剧下降至20\%以下，萌芽期新叶呈现红褐色反射，成熟期密集的枝叶交错形成复杂的阴影模式。这些剧烈的物候变化使得基于单一时相训练的模型在其他物候期性能显著下降，严重制约了橡胶林的自动化监测。

针对树冠分割任务，已有研究发展了两大类方法：传统方法和深度学习方法。根据Ke和Quackenbush\cite{ke2011review}的综述，传统树冠分割方法主要包括三种类型：谷线跟踪、区域生长和分水岭分割。谷线跟踪方法依赖森林内部的阴影间隙来分割树冠\cite{gougeon1998automatic}，在成熟针叶林中表现良好，但在自遮蔽树冠或复杂森林条件下精度下降。区域生长方法广泛用于树冠分割，通常利用树冠的光谱特征进行分割\cite{culvenor2002tida, erikson2003segmentation}。该算法需要种子点开始生长和停止生长的准则，但在复杂混交林中准确检测树顶和确定最优阈值都很困难。分水岭分割方法主要基于图像中的边缘信息，但图像噪声会导致过分割问题。

为改善传统分水岭分割的过分割问题，研究者提出了两类改进方法。第一类是多尺度分析方法。Jing等人\cite{jing2012automated}基于三个主要尺度的高斯滤波器平滑图像进行多尺度分水岭分割，然后从不同尺度的结果中选择最优分割。Yang等人\cite{yang2014automated}基于计算的梯度图像进行分水岭分割，采用多尺度阈值生成多尺度分割结果。第二类是标记控制分水岭分割，其中标记是基于局部最大值检测的树顶。Wang等人\cite{wang2004automated, wang2010crown}检测了两种不同类型的树顶：光谱局部最大值和空间局部最大值。Lamar等人\cite{lamar2005automated}和Tong等人\cite{tong2021improved}利用空间局部最大值检测树顶。

近年来，深度学习方法被广泛应用于树冠分割任务\cite{zhao2023review}。一类深度学习方法将深度学习与分水岭分割相结合。例如，Lassalle等人\cite{lassalle2022cnn}利用CNN模型计算距离图，指示像素到最近树冠边界的距离，然后通过在距离图中定位局部最大值来识别树顶。Freudenberg等人\cite{freudenberg2022individual}利用U-Net\cite{ronneberger2015u}预测树冠掩膜、树冠轮廓和距离图。另一类深度学习方法采用实例分割技术，其中Mask R-CNN\cite{he2017mask}是树冠分割研究中使用最广泛的深度学习模型\cite{braga2020amazon, hao2021individual, ball2023detectree2}。

尽管深度学习方法通常优于传统方法，但在橡胶树冠分割中仍面临三个关键挑战。首先，树冠轮廓不清晰问题。成熟橡胶树冠幅可达8-12米，相邻树木的枝叶经常交错重叠，形成连续的冠层覆盖，单株树木的边界变得极其模糊。传统的边缘检测算法难以准确提取单株树冠边界，深度学习模型也容易产生欠分割现象。其次，物候动态引发的特征漂移问题。橡胶树在落叶期、萌芽期、生长期和成熟期表现出截然不同的光谱反射特征，多时相NDVI曲线呈现非连续性变化，使得基于单一时相训练的模型在其他物候期性能急剧下降。第三，非目标植被的干扰问题。橡胶园常见的飞机草、薇甘菊等杂草在近红外波段的反射特征与橡胶幼树高度相似，形状相似性使得杂草的叶片形态与橡胶幼树相近，传统的光谱分析方法无法有效区分，导致严重的误分类现象。

针对上述挑战，本文提出了一种基于物理约束状态空间模型的橡胶树冠分割框架（CSAF）。该框架专门设计来解决橡胶树遥感监测中的三个核心问题：1）针对树冠轮廓不清晰问题，我们设计了基于状态空间模型的边界增强模块（GM-Mamba），首次将状态空间模型引入树冠分割任务，通过傅里叶域增强和选择性扫描机制建模树冠边界的长程空间依赖关系，有效捕获橡胶树叶片沿枝条方向的连续性特征；2）针对物候动态引发的特征漂移问题，我们开发了多智能体持续学习机制（MASA-Optimizer），通过自适应记忆管理和经验回放避免跨物候期训练中的灾难性遗忘，确保模型在极端物候变化下的稳定性；3）针对非目标植被干扰问题，我们构建了基于泊松方程的物理约束模块（MPC-Poisson），利用扩散过程的形态学先验约束分割结果的空间连贯性和结构合理性，有效抑制形状相似、纹理相近、分布模式复杂的杂草干扰。



为评估所提出方法的性能，我们在构建的跨物候期橡胶树数据集上进行了全面实验，该数据集涵盖萌芽期、生长期、成熟期和落叶期四个完整阶段。与广泛认可的基准模型相比，我们的CSAF框架在RT-Set数据集上实现了AP50为76.63\%的性能，相比基线模型提升了6.11个百分点。特别值得注意的是，在落叶期样本仅占训练集4.7\%的极端不平衡情况下，CSAF在年度计数任务中仍保持91.16\%的平均精度，显著优于对比方法的63.75\%。此外，我们还分析了超参数设置和图像空间分辨率对分割精度的影响，为未来用户在分割任务中应用所提出模型提供指导。

本文的其余部分组织如下：第2节提供了研究区域描述、所提出树冠分割方法的详细信息以及树冠分割评估指标的介绍。第3节展示了实验结果，包括分割结果描述、分割误差分析以及与其他方法的比较。第4节分析了所提出分割方法的参数设置，分析了空间分辨率的影响，探讨了森林研究的前景，并提出了可能的进一步改进。第5节为研究提供了总体结论。





\section{材料与方法}

\subsection{研究区域}
研究区域位于中国海南省儋州市西北部，地理坐标为19°31'50.59''N，109°28'52.62''E，隶属于中国热带农业科学院橡胶研究所建立的实验林区。该区域属于南亚热带季风气候区，气候特征为全年温暖，年内温差较小。年平均气温介于22.5°C至25.6°C之间，其中1月为最冷月，7月为最热月。年日照时数为1780-2600小时，年降水量在900-2400毫米之间，为橡胶树生长提供了良好的水热条件\cite{priyadarshan2017biology}。

值得注意的是，海南岛位于南海北缘，是经常受台风影响的沿海岛屿之一。主要台风季节为6月至10月。虽然儋州位于岛屿西北部，远离典型的台风登陆区，但仍经常受到热带气旋及其外围云系的影响。这些扰动包括极端风力和强降雨，常常造成树木倒伏和冠层破碎等结构性损害。作为典型的热带经济作物，橡胶树的冠层结构对风力扰动高度敏感。因此，在该区域进行野外观测和遥感研究，不仅能够收集不同扰动强度下的冠层响应数据，还为评估所提出的树冠分割和生态监测框架在复杂背景条件下的稳健性和泛化能力提供了理想的测试平台。此外，该区域为评估框架在极端天气情景下的适应性和监测精度提供了合适的环境，在灾害响应和森林健康评估方面具有巨大的应用潜力。研究区域的具体位置如图\ref{fig:study_area}所示。

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\linewidth,keepaspectratio]{Study Area.pdf}
    \caption{研究区域位置图。}
    \label{fig:study_area}
\end{figure}


\subsection{数据集构建}
为了全面评估所提出方法的性能和泛化能力，本研究在四个涵盖不同地理环境和树种的数据集上进行了广泛实验，包括热带橡胶种植园、温带果树园、城市绿化带和北方针叶林等多样化生态系统。表\ref{tab:datasets}详细描述了各数据集的基本信息。

\begin{table*}[htbp]
\renewcommand{\arraystretch}{1.2}
\caption{实验数据集详细信息}
\label{tab:datasets}
\centering
\scriptsize
\begin{tabular}{p{2.0cm}p{1.8cm}p{2.2cm}p{1.8cm}p{2.0cm}p{1.8cm}p{2.0cm}}
\toprule
\textbf{数据集} & \textbf{地理位置} & \textbf{主要树种} & \textbf{图像数量} & \textbf{数据来源} & \textbf{采集时间} & \textbf{主要特征} \\
\midrule
橡胶树数据集 & 中国海南 & \textit{Hevea} & 5,281 & UAV (80m) & 2023.11- & 极端物候 \\
(RT-Set) & 儋州 & \textit{brasiliensis} & (3697/ & Phantom 4 & 2024.07 & 变化，四个 \\
 & 19°31'N &  & 1056/528) & RTK &  & 完整周期 \\
\midrule
杨梅树数据集 & 中国浙江 & \textit{Myrica} & 2,430 & UAV & 2023.05- & 球形冠层 \\
(BT-Set) & 永嘉 & \textit{rubra} & (1701/ & DJI & 2024.04 & 结构，强 \\
 & 28°17'N &  & 486/243) & Phantom 4 &  & 阴影效应 \\
\midrule
城市树冠 & 西班牙 & 混合阔叶/ & 1,420 & 机载传感器 & 2022.06- & 复杂城市 \\
数据集 & 莱里达 & 针叶 & (994/ & 高分辨率 & 2023.09 & 背景，人工 \\
(UT-Set) & 41°37'N &  & 284/142) & 航拍 &  & 修剪形状 \\
\midrule
魁北克森林 & 加拿大 & \textit{Picea} & 5,321 & 公开数据集 & 2023.07- & 高密度针 \\
(CTF-Set) & 魁北克 & spp.混合 & (仅测试集) & 温带混 & 2024.06 & 叶林，24个 \\
 & 45°59'N & 落叶/针叶 &  & 交林 &  & 树种 \\
\bottomrule
\end{tabular}
\end{table*}

\textbf{橡胶树数据集（RT-Set）}：该数据集采集自中国海南省儋州市中国热带农业科学院橡胶研究所建立的实验林区，涵盖萌芽期、生长期、成熟期和落叶期四个完整物候周期。数据采集使用DJI Phantom 4 RTK无人机，配备1英寸CMOS传感器（2000万有效像素），飞行高度80米，横向和纵向重叠率均为85\%。该数据集的独特之处在于其极端的季节性变化特征，落叶期样本仅占训练集4.7\%，为评估模型在物候动态条件下的稳健性提供了理想的测试平台。

\textbf{杨梅树数据集（BT-Set）}：采集自中国浙江省永嘉县大洋山森林公园（28°17'N–28°19'N, 120°26'E–120°28'E），该区域属于中亚热带常绿阔叶林带。杨梅树具有独特的球形冠层结构和密集的分枝模式，但强阴影效应和密集的林下植被与树冠具有高度光谱相似性，使得该数据集在实例分割任务中极具挑战性。

\textbf{城市树冠数据集（UT-Set）}：来源于西班牙莱里达市（41°37'N, 0°37'E）的地中海城市环境，涵盖59公顷的异质城市基础设施。该数据集包含多种城市环境，如主要街道、住宅区、公园和密集建筑区，提供了广泛的场景多样性和结构遮挡。数据集包含14,772个标注树冠，其中许多被建筑物、车辆或街道设施部分遮挡。

\textbf{魁北克森林数据集（CTF-Set）}：采集自加拿大魁北克省圣伊波利特（45°59'N, 74°00'W）的温带混交林，研究区域跨越丘陵、湿地和湖泊等多样地形，支持典型的北美未管理森林中的多种落叶和针叶树种。该数据集的关键挑战在于冠层内的高物种多样性，包含十多个树种和属，具有独特但经常重叠的冠层结构。

表\ref{tab:phenology}进一步详细描述了RT-Set数据集中不同物候期的样本分布情况，展现了橡胶树极端季节性变化的数据特征。

\begin{table}[htbp]
\renewcommand{\arraystretch}{1.2}
\caption{橡胶树数据集物候期分布}
\label{tab:phenology}
\centering
\begin{tabular}{lccc}
\toprule
\textbf{物候期} & \textbf{训练集} & \textbf{验证集} & \textbf{测试集} \\
\midrule
萌芽期 & 1,074 & 271 & 131 \\
生长期 & 1,381 & 286 & 136 \\
成熟期 & 1,068 & 243 & 120 \\
落叶期 & 174 & 256 & 141 \\
\midrule
\textbf{总计} & \textbf{3,697} & \textbf{1,056} & \textbf{528} \\
\bottomrule
\end{tabular}
\end{table}

\section{Methodology}

\subsection{CSAF框架总体设计}

CSAF框架采用模块化架构设计，由特征提取骨干网络和三个专门化处理模块组成。骨干网络基于改进的ResNet-50实现，通过集成拉普拉斯变换和残差块构建多尺度特征金字塔。具体实现中，输入图像$I \in \mathbb{R}^{B \times 3 \times H \times W}$首先经过拉普拉斯变换模块进行边缘增强处理。该模块在频域中应用3×3和5×5拉普拉斯算子，通过二维快速傅里叶变换将输入转换为复数表示$\hat{I} = \text{FFT2D}(I)$，分别对实部和虚部应用拉普拉斯卷积核：

\begin{equation}
\begin{aligned}
L_{3 \times 3} &= \begin{bmatrix} 1 & 0 & 1 \\ 0 & -4 & 0 \\ 1 & 0 & 1 \end{bmatrix}, \quad L_{5 \times 5} = \begin{bmatrix} -2 & -4 & -4 & -4 & -2 \\ -4 & 0 & 8 & 0 & -4 \\ -4 & 8 & 24 & 8 & -4 \\ -4 & 0 & 8 & 0 & -4 \\ -2 & -4 & -4 & -4 & -2 \end{bmatrix}
\end{aligned}
\end{equation}

经过拉普拉斯变换后，实部和虚部特征在通道维度拼接形成$F_{lap} \in \mathbb{R}^{B \times 6 \times H \times W}$的增强特征表示。随后通过五个残差块构建特征金字塔$\{F_{p2}, F_{p3}, F_{p4}, F_{p5}, F_{p6}\}$，其中每个残差块包含两个3×3卷积层、批归一化和ReLU激活函数，输出通道数统一为256。各层特征的空间分辨率分别为原图的1/4、1/8、1/16、1/32和1/64。

三个专门化模块并行处理特征金字塔的输出。GM-Mamba模块针对每个尺度的特征$F_{pi} \in \mathbb{R}^{B \times 256 \times H_i \times W_i}$，首先通过einops库的rearrange函数将空间维度重塑为序列形式$F_{seq} \in \mathbb{R}^{B \times (H_i \times W_i) \times 256}$，然后输入到配置为d\_model=256、n\_layer=1、d\_state=4、dt\_rank=4的Mamba模块进行长程依赖建模。MASA-Optimizer模块维护一个容量为100的MemoryBuffer，存储历史时相的特征表示，通过iCaRLNet网络动态调整遗忘因子$\alpha$。MPC-Poisson模块采用四层多层感知机网络，输入归一化坐标$(x,y) \in [-1,1]^2$，输出树冠存在概率$u(x,y) \in [0,1]$，通过自动微分计算泊松方程残差$\nabla^2 u + f(x,y) = 0$实现物理约束。

框架的前向传播过程如下：给定输入图像$I$，首先通过拉普拉斯变换和残差块提取特征金字塔$\{F_{p2}, F_{p3}, F_{p4}, F_{p5}, F_{p6}\}$；然后GM-Mamba模块对每个尺度特征进行序列建模，输出增强的边界特征$\{F'_{p2}, F'_{p3}, F'_{p4}, F'_{p5}, F'_{p6}\}$；MASA-Optimizer根据当前特征统计量和历史经验计算遗忘因子$\alpha_t$；MPC-Poisson模块生成物理约束权重$W_{physics} \in \mathbb{R}^{B \times 1 \times H \times W}$；最终通过加权融合生成分割结果$S = \alpha_t \cdot F'_{enhanced} + (1-\alpha_t) \cdot F_{replay} \odot W_{physics}$，其中$F_{enhanced}$为GM-Mamba增强后的特征，$F_{replay}$为历史特征回放，$\odot$表示逐元素乘法。

\subsection{GM-Mamba边界增强模块}

GM-Mamba模块专门设计用于建模橡胶树冠边界的长程空间依赖关系。橡胶树成熟期的枝条可延伸12-15米，形成连续的冠层覆盖，传统卷积网络的局部感受野难以捕获这种大尺度的边界连续性。该模块基于状态空间模型的线性复杂度优势，将二维空间特征重塑为一维序列进行处理，特别适合捕获沿枝条方向延伸的冠层边界特征。GM-Mamba模块对特征金字塔的中间三层$\{F_{p3}, F_{p4}, F_{p5}\}$进行独立处理，避免过细尺度的噪声干扰和过粗尺度的细节丢失。每个尺度特征$F_{pi} \in \mathbb{R}^{B \times 256 \times H_i \times W_i}$通过einops库的rearrange函数重塑为序列表示：

\begin{equation}
\begin{aligned}
X_{pi} &= \text{rearrange}(F_{pi}, 'b c h w \rightarrow b (h w) c') \\
&\in \mathbb{R}^{B \times L_i \times 256}
\end{aligned}
\end{equation}

其中$L_i = H_i \times W_i$为序列长度，空间邻接关系通过行优先扫描保持。

为了建模橡胶树冠边界的长程依赖关系，采用线性时不变状态空间模型。对于输入序列$X \in \mathbb{R}^{B \times L \times D}$，离散时间状态方程定义为：

\begin{equation}
\begin{aligned}
h_k &= \bar{A} h_{k-1} + \bar{B} x_k \\
y_k &= C h_k + D x_k
\end{aligned}
\end{equation}

其中$h_k \in \mathbb{R}^N$为第$k$步的隐状态，$N=4$为状态维度。状态转移矩阵$\bar{A} \in \mathbb{R}^{N \times N}$编码空间位置间的依赖关系，输入矩阵$\bar{B} \in \mathbb{R}^{N \times D}$控制当前输入对状态的影响，输出矩阵$C \in \mathbb{R}^{D \times N}$将隐状态映射为输出，前馈矩阵$D \in \mathbb{R}^{D \times D}$提供直接的输入-输出连接。为了适应不同空间位置的边界特征变化，引入选择性机制生成时变参数，输入序列经过线性投影层x\_proj生成三组参数：

\begin{equation}
[\Delta, B, C] = \text{split}(\text{x\_proj}(X), [R, N, N])
\end{equation}

其中$\Delta \in \mathbb{R}^{B \times L \times R}$为时间步长参数（$R=4$为步长参数的秩），$B \in \mathbb{R}^{B \times L \times N}$和$C \in \mathbb{R}^{B \times L \times N}$为时变输入输出参数。时间步长参数通过softplus激活确保正值：$\Delta = \text{softplus}(\text{dt\_proj}(\Delta))$。

连续时间状态转移矩阵$A \in \mathbb{R}^{D \times N}$采用对数参数化以确保离散化后的数值稳定性：

\begin{equation}
A = -\exp(A_{\log})
\end{equation}

负指数形式保证了系统的稳定性，这是Mamba架构的关键设计。离散化过程采用零阶保持器(ZOH)方法，通过批量矩阵指数运算实现：

\begin{equation}
\begin{aligned}
\bar{A} &= \exp(\Delta A) \\
\bar{B} &= (\Delta A)^{-1}(\bar{A} - I) \Delta B
\end{aligned}
\end{equation}

矩阵指数通过Padé近似或特征值分解计算，确保数值稳定性。核心的选择性扫描算法基于Mamba的并行实现，通过关联扫描(associative scan)实现高效的并行计算：

\begin{equation}
Y = \text{SelectiveScan}(X, \Delta, A, B, C, D)
\end{equation}

该算法避免了递归计算的序列依赖，时间复杂度为$O(BLD)$，相比自注意力机制的$O(BL^2D)$复杂度显著降低，使得模型能够处理高分辨率遥感图像。

完整的GM-Mamba块采用预归一化残差结构，首先对输入进行RMS归一化，然后通过输入投影层分割为两个分支，其中$D_{inner} = 2 \times 256 = 512$为内部扩展维度。主分支经过一维卷积（核大小为4）和SiLU激活，然后输入状态空间模型，最终通过门控机制与残差分支融合并与输入进行残差连接。处理后的序列特征通过逆变换重塑回空间维度：

\begin{equation}
\begin{aligned}
F'_{pi} &= \text{rearrange}(Y_{pi}, 'b (h w) c \rightarrow b c h w', \\
&\quad h=H_i, w=W_i)
\end{aligned}
\end{equation}

增强特征与原始特征通过可学习权重$\gamma$进行残差融合：

\begin{equation}
F_{enhanced} = F_{pi} + \gamma \cdot F'_{pi}
\end{equation}

其中$\gamma$初始化为0.1，在训练过程中自适应调整。该设计特别适合捕获橡胶树冠沿枝条方向的连续性特征，这些特征在大尺度空间范围内表现出强烈的方向性和连续性，正是传统卷积网络难以有效建模的长程依赖关系。

\subsection{MPC-Poisson物理约束模块}

MPC-Poisson模块专门设计用于抑制橡胶园中的杂草干扰，基于橡胶树冠与杂草在空间分布形态上的显著差异。橡胶树冠呈现规则的近圆形或椭圆形形态，其空间分布遵循扩散过程的物理规律，而飞机草、薇甘菊等杂草则表现为不规则的分散分布。传统深度学习方法主要依赖数据驱动的特征学习，在光谱相似的情况下容易产生误分类，缺乏对物理规律的显式约束。该模块引入物理信息神经网络的思想，将扩散方程的物理约束嵌入到神经网络中，通过约束分割结果的空间连贯性和形态合理性来有效抑制背景干扰。

模块将树冠存在概率场建模为稳态扩散过程，满足二维泊松方程：

\begin{equation}
\nabla^2 u(x,y) = \frac{\partial^2 u}{\partial x^2} + \frac{\partial^2 u}{\partial y^2} = f(x,y)
\end{equation}

其中$u(x,y)$表示位置$(x,y)$处的树冠存在概率，$f(x,y)$为源项函数。在实际实现中，使用四层多层感知机网络来参数化概率场函数：

\begin{equation}
\begin{aligned}
u(x,y) &= \mathcal{N}_\theta(x,y) \\
&= \sigma(\text{Linear}_4(\text{Swish}(\text{Linear}_3(\text{Swish}( \\
&\quad \text{Linear}_2(\text{Swish}(\text{Linear}_1(x,y))))))))
\end{aligned}
\end{equation}

其中网络结构为$\mathbb{R}^2 \rightarrow \mathbb{R}^{64} \rightarrow \mathbb{R}^{32} \rightarrow \mathbb{R}^{16} \rightarrow \mathbb{R}^1$，采用Swish激活函数$\text{Swish}(x) = x \cdot \sigma(x)$以保证高阶导数的连续性，输出层使用Sigmoid函数$\sigma(x) = 1/(1+e^{-x})$将结果约束在$[0,1]$区间内。网络的输入为归一化的空间坐标$(x,y) \in [-1,1]^2$，通过线性变换从像素坐标$(i,j)$获得：

\begin{equation}
\begin{aligned}
x &= \frac{2i}{H-1} - 1 \\
y &= \frac{2j}{W-1} - 1
\end{aligned}
\end{equation}

物理约束通过自动微分技术实现。对于网络输出$u(x,y)$，计算其二阶偏导数：

\begin{equation}
\begin{aligned}
u_{xx} &= \frac{\partial^2 u}{\partial x^2}, \quad u_{yy} = \frac{\partial^2 u}{\partial y^2}
\end{aligned}
\end{equation}

泊松方程残差定义为：

\begin{equation}
\mathcal{R}_{Poisson}(x,y) = u_{xx} + u_{yy} + f(x,y)
\end{equation}

源项函数$f(x,y)$设计为多高斯函数的线性组合，模拟树冠从多个中心向外扩散的生长模式：

\begin{equation}
\begin{aligned}
f(x,y) &= -\sum_{k=1}^{K} \alpha_k \exp\left(-\frac{(x-x_k)^2 + (y-y_k)^2}{2\sigma_k^2}\right)
\end{aligned}
\end{equation}

其中$K$为高斯核的数量，$(x_k, y_k)$为第$k$个高斯核的中心，$\alpha_k$为强度系数，$\sigma_k$为标准差。

为了增强约束的有效性，模块采用多尺度物理约束策略。在三个不同的空间分辨率$\{H/4, H/8, H/16\}$下分别施加泊松方程约束：

\begin{equation}
\mathcal{L}_{physics} = \sum_{s=1}^{3} w_s \frac{1}{N_s} \sum_{i=1}^{N_s} |\mathcal{R}_{Poisson}^{(s)}(x_i, y_i)|^2
\end{equation}

其中$w_s = [0.5, 0.3, 0.2]$为尺度权重，$N_s$为第$s$尺度的采样点数。对于检测到的树冠边界区域$\partial \Omega$，施加Dirichlet边界条件：

\begin{equation}
u(x,y)|_{\partial \Omega} = 0.5
\end{equation}

边界条件损失定义为：

\begin{equation}
\begin{aligned}
\mathcal{L}_{boundary} &= \frac{1}{N_{boundary}} \sum_{(x,y) \in \partial \Omega} |u(x,y) - 0.5|^2
\end{aligned}
\end{equation}

MPC-Poisson模块与主分割网络的集成通过空间注意力机制实现。模块输出的概率场$u(x,y)$经过双线性插值调整到与特征图相同的空间分辨率
然后作为空间注意力权重与原始特征相乘：

\begin{equation}
F_{constrained} = F \odot W_{physics}
\end{equation}

其中$\odot$表示逐元素乘法。总损失函数结合了数据拟合损失、物理约束损失和边界条件损失：

\begin{equation}
\mathcal{L}_{MPC} = \mathcal{L}_{data} + \lambda_{phy} \mathcal{L}_{physics} + \lambda_{bc} \mathcal{L}_{boundary}
\end{equation}

其中$\lambda_{phy} = 0.1$和$\lambda_{bc} = 0.01$为平衡权重。该设计有效地将橡胶树冠扩散生长的物理先验融入到深度学习模型中，通过物理约束显著提升了模型对不规则杂草干扰的抑制能力。

\subsection{MASA-Optimizer持续学习机制}

MASA-Optimizer模块专门设计用于解决跨物候期训练中的灾难性遗忘问题。橡胶树在不同物候期表现出极端的光谱变异性，从新叶期的鲜绿色（NDVI=0.85）到落叶期的棕黄色（NDVI=0.25），光谱反射率变化幅度可达40\%以上。传统的序贯学习方法在学习新时相特征时往往会覆盖已学习的历史时相知识，导致模型在之前时相上的性能急剧下降。该模块基于多智能体协作的自适应遗忘因子优化机制，通过动态调节新旧知识的融合比例来实现有效的持续学习。

模块维护一个容量为100的MemoryBuffer来存储历史时相的特征表示，采用iCaRLNet网络架构进行特征编码和回放。核心是引入可学习的遗忘因子$\alpha \in [0,1]$来控制历史知识与新知识的平衡。持续学习的损失函数定义为：

\begin{equation}
\mathcal{L}_{continual} = \alpha \mathcal{L}_{new} + (1-\alpha) \mathcal{L}_{replay}
\end{equation}

其中$\mathcal{L}_{new}$为当前时相的损失，$\mathcal{L}_{replay}$为从经验缓冲区采样的历史时相回放损失。遗忘因子$\alpha$的优化目标是最小化在所有历史时相上的累积性能退化：

\begin{equation}
\begin{aligned}
\alpha^* &= \arg\min_{\alpha} \sum_{t=1}^{T} w_t \cdot \mathcal{L}_t(\theta_{\alpha}) \\
&\text{s.t.} \quad \alpha \in [0,1], \quad \sum_{t=1}^{T} w_t = 1
\end{aligned}
\end{equation}

其中$w_t$为时相权重，$\mathcal{L}_t(\theta_{\alpha})$为使用遗忘因子$\alpha$训练后模型在第$t$个时相上的损失。为了有效求解这一优化问题，模块设计了三个专门化的智能体：模拟退火智能体（SimulatedAnnealing）、强化学习智能体（QLearningAgent）和遗传算法智能体（GeneticAlgorithm），分别负责全局探索、策略学习和局部精调。

MASA-Optimizer采用三阶段协作优化策略，根据训练进度动态切换优化算法。第一阶段（前30\%迭代）使用模拟退火算法进行全局探索，初始化参数为$T_0 = 100$、$T_{min} = 1$、冷却率$\gamma = 0.95$。在当前解$\alpha_{old}$附近生成候选解$\alpha_{new} = \alpha_{old} + \mathcal{N}(0, 0.1^2)$，候选解的接受概率为：

\begin{equation}
P_{accept} = \min\left(1, \exp\left(-\frac{\Delta E}{T_k}\right)\right)
\end{equation}

其中$\Delta E = \mathcal{L}_{continual}(\alpha_{new}) - \mathcal{L}_{continual}(\alpha_{old})$，温度$T_k = T_0 \cdot \gamma^k$按几何级数衰减。该阶段通过高温下的随机接受机制避免陷入局部最优，确保充分的全局搜索。

第二阶段（30\%-70\%迭代）将$\alpha$优化建模为马尔可夫决策过程，使用Q-learning算法学习最优调整策略。状态空间离散化为100个状态，通过$s_t = \text{int}(\mathcal{L}_{total} \times 10)$将总损失映射到状态索引。动作空间包含3个动作：$a_0$（减小$\alpha$）、$a_1$（增大$\alpha$）、$a_2$（保持$\alpha$不变）。Q表初始化为$100 \times 3$的零矩阵，采用$\epsilon$-贪婪策略进行动作选择，其中$\epsilon = 0.1$。Q值更新遵循标准的Q-learning规则：

\begin{equation}
\begin{aligned}
Q(s_t, a_t) &\leftarrow (1-\alpha_{lr}) Q(s_t, a_t) \\
&\quad + \alpha_{lr}[r_t + \gamma \max_{a'} Q(s_{t+1}, a')]
\end{aligned}
\end{equation}

其中$\alpha_{lr} = 0.1$为学习率，$\gamma = 0.9$为折扣因子。奖励函数设计为：

\begin{equation}
r_t = -\mathcal{L}_{continual}(\alpha_t) - \lambda_{stab}|\alpha_t - \alpha_{t-1}|
\end{equation}

其中$\lambda_{stab} = 0.01$为稳定性惩罚权重，防止$\alpha$值剧烈波动。

第三阶段（70\%-100\%迭代）使用遗传算法进行局部精调，种群规模设置为20，最大进化代数为10。适应度函数定义为$f(\alpha) = -\text{mean}(\mathcal{L}_{history}[-10:])$，使用最近10次损失的负均值。选择操作采用锦标赛选择（$k=3$），交叉操作使用算术交叉：

\begin{equation}
\begin{aligned}
\alpha_{child1} &= w \cdot \alpha_{parent1} + (1-w) \cdot \alpha_{parent2} \\
\alpha_{child2} &= (1-w) \cdot \alpha_{parent1} + w \cdot \alpha_{parent2}
\end{aligned}
\end{equation}

其中$w \sim \mathcal{U}(0,1)$为随机权重。变异操作采用高斯变异$\alpha_{mutated} = \alpha + \mathcal{N}(0, 0.02^2)$，变异率设置为0.1。该阶段在前两阶段确定的优良区域内进行精细搜索，确保收敛到局部最优解。


\begin{algorithm}[htbp]
\caption{MASA-Optimizer三阶段优化算法}
\label{alg:masa}
\begin{algorithmic}[1]
\Require 多时相数据集 $\mathcal{D}=\{D_1, D_2, \dots, D_T\}$，初始遗忘因子 $\alpha_0$
\Ensure 遗忘因子序列 $\{\alpha_1, \dots, \alpha_T\}$

\State 初始化：缓冲区 $\mathcal{B}$、Q表 $Q$、损失历史 $\mathcal{H}$
\State 设置阶段边界：$t_1 = 0.3T$，$t_2 = 0.7T$
\For{$t = 1$ to $T$}
  \State 计算当前损失 $\mathcal{L}_{\text{cur}}$ 和回放损失 $\mathcal{L}_{\text{rep}}$
  
  \If{$t \leq t_1$} \Comment{阶段1：模拟退火}
    \State 根据能量差更新 $\alpha_t$
  
  \ElsIf{$t \leq t_2$} \Comment{阶段2：强化学习}
    \State 构建状态 $s_t$，选择并执行动作 $a_t$
    \State 更新遗忘因子 $\alpha_t$ 与 Q值
  
  \Else \Comment{阶段3：遗传算法}
    \State 初始化种群 $P$
    \For{每一代}
      \State 选择、交叉、变异并更新 $P$
    \EndFor
    \State 选择最优个体为 $\alpha_t$
  \EndIf

  \State 联合训练并更新模型参数 $\theta$
  \State 更新缓冲区 $\mathcal{B}$ 与历史损失 $\mathcal{H}$
\EndFor
\end{algorithmic}
\end{algorithm}


经验回放机制采用时相感知的重要性采样策略，维护一个固定容量的MemoryBuffer来存储历史时相的特征表示。对于缓冲区中的样本$(x_i, y_i, t_i)$，其采样概率定义为：

\begin{equation}
\begin{aligned}
p(x_i, y_i, t_i) &\propto \exp(-\beta \cdot \text{TV}(t_i, t_{current})) \\
\text{TV}(t_i, t_j) &= \frac{1}{2}\sum_{k=1}^{K}|\rho_k(t_i) - \rho_k(t_j)|
\end{aligned}
\end{equation}

其中$\text{TV}(\cdot, \cdot)$为时相变异度量，$\rho_k(t)$为第$k$个光谱波段在时相$t$的平均反射率，$\beta = 1.0$为温度参数。该采样策略确保与当前时相相似的历史样本有更高的被选中概率，提高回放的有效性。

模块与主网络的集成通过iCaRLNet架构实现。在每个训练步骤中，首先使用当前的遗忘因子$\alpha_t$计算联合损失$\mathcal{L}_{joint} = \alpha_t \mathcal{L}_{new} + (1-\alpha_t) \mathcal{L}_{replay}$，然后通过标准的反向传播更新网络参数$\theta$。同时，三个智能体根据当前的损失变化和特征统计量协作调整遗忘因子，形成一个闭环的自适应优化系统。整个MASA-Optimizer机制通过多智能体协作的自适应调节，有效解决了跨物候期训练中的灾难性遗忘问题，显著提升了模型在多时相数据上的泛化能力和鲁棒性。



\section{Experimental Setup}

\subsection{数据集}

本研究在三个自建数据集和三个公开数据集上进行了广泛实验，涵盖了从热带橡胶种植园到温带混交林的多样化生态系统。自建数据集包括：(1) 橡胶树数据集（RT-Set），包含2023年11月3日至2024年7月8日收集的图像，共3,697张训练图像、1,056张验证图像和528张测试图像，代表萌芽期、生长期、成熟期和落叶期四个不同物候期；(2) 计数样地数据集（RRA-Set），从2023年3月至2024年3月使用高频时间采样收集，包含195张图像，主要用于评估树冠计数性能；(3) 灾后橡胶树数据集（DRT-Set），涵盖2024年8月9日至2025年2月6日，包含受台风影响的橡胶种植园图像。

公开数据集包括：(1) 杨梅数据集（BT-Set），在浙江省永嘉县大洋山森林公园收集，包含1,701张训练图像、486张验证图像和243张测试图像；(2) 城市树冠数据集（UT-Set），来自西班牙莱里达市，包含994张训练图像、284张验证图像和142张测试图像；(3) 加拿大树叶物候数据集（CTF-Set），在魁北克省圣伊波利特的温带混交林收集，包含5,321张测试图像，涵盖十多个树种。

\subsection{评估指标}

对于实例分割任务，采用COCO评估协议中的平均精度（AP）指标，包括AP（IoU阈值0.5:0.95的平均值）、AP50（IoU阈值0.5）、AP75（IoU阈值0.75）、APm（中等尺寸目标）和APl（大尺寸目标）。对于计数任务，采用准确率指标：

\begin{equation}
\text{Accuracy} = 1 - \frac{|\text{Predicted} - \text{Real}|}{\text{Real}}
\end{equation}

其中Predicted为模型预测的树冠数量，Real为人工计数的真实树冠数量。

\section{Experimental Results}

\subsection{消融实验}

为了研究CSAF中各模块的贡献和协作机制，设计了八组消融实验。如表\ref{tab:ablation}所示，每个单独模块都显著提升了性能。GM-Mamba通过多尺度边缘增强将AP50提升至72.59\%（+2.07\%），MASA-Optimizer通过动态特征适应将AP50提升至72.81\%（+2.29\%），MPC-Poisson通过物理约束将AP50提升至72.01\%（+1.49\%）。

模块组合进一步增强了模型性能。GM-Mamba + MASA-Optimizer在复杂轮廓场景下达到74.43\%的AP50（+3.91\%），GM-Mamba + MPC-Poisson在非目标植被干扰场景下将AP75提升至73.89\%（+3.37\%）。CDPO-E引擎（MASA-Optimizer + MPC-Poisson）通过时空协作优化达到74.51\%的AP50（+3.99\%）。完整的CSAF模型（集成所有三个模块）取得最优结果，AP50达到76.63\%（+6.11\%），AP75达到44.11\%（+5.93\%）。

\begin{table}[htbp]
\centering
\caption{CSAF及其子模块的消融实验结果}
\label{tab:ablation}
\begin{tabular}{cccc|ccccc}
\hline
\multicolumn{4}{c|}{模块配置} & \multicolumn{5}{c}{性能指标 (\%)} \\
\hline
GM-Mamba & MASA-Opt & MPC-Poisson & 基线 & AP & AP50 & AP75 & APm & APl \\
\hline
& & & \checkmark & 38.18 & 70.52 & 38.18 & 40.25 & 36.82 \\
\checkmark & & & & 39.41 & 72.59 & 39.41 & 41.58 & 38.15 \\
& \checkmark & & & 39.63 & 72.81 & 39.63 & 41.82 & 38.37 \\
& & \checkmark & & 38.67 & 72.01 & 38.67 & 40.74 & 37.31 \\
\checkmark & \checkmark & & & 40.85 & 74.43 & 40.85 & 43.01 & 39.58 \\
\checkmark & & \checkmark & & 40.27 & 73.89 & 40.27 & 42.43 & 38.94 \\
& \checkmark & \checkmark & & 40.93 & 74.51 & 40.93 & 43.09 & 39.66 \\
\checkmark & \checkmark & \checkmark & & 42.29 & 76.63 & 44.11 & 44.52 & 40.95 \\
\hline
\end{tabular}
\end{table}

\subsection{单一森林跨物候期和跨物种的树冠分割效果}

在BT-Set数据集上，由于树冠轮廓相对清晰且个体树木分布稀疏，所有评估模型都达到了较高的分割精度，AP50分数均超过80\%。然而，在RT-Set数据集上，该数据集具有更高的冠层密度、明显的物候变化、模糊的树冠边界和大量非目标植被干扰，CSAF表现出明显的性能优势。如表\ref{tab:single_forest}所示，CSAF在AP50方面超越所有基线模型，比DT模型提升5.35\%，比SL模型提升超过30\%。

\begin{table}[htbp]
\centering
\caption{CSAF与基线模型在BT-Set和RT-Set数据集上的性能比较}
\label{tab:single_forest}
\begin{tabular}{l|ccccc|ccccc}
\hline
\multirow{2}{*}{模型} & \multicolumn{5}{c|}{BT-Set} & \multicolumn{5}{c}{RT-Set} \\
& AP & AP50 & AP75 & APm & APl & AP & AP50 & AP75 & APm & APl \\
\hline
CMR & 58.41 & 86.79 & 67.33 & 60.15 & 56.67 & 38.18 & 70.52 & 38.18 & 40.25 & 36.82 \\
SW & 57.83 & 86.21 & 66.75 & 59.57 & 56.09 & 37.94 & 70.28 & 37.94 & 40.01 & 36.58 \\
HTC & 58.67 & 87.05 & 67.59 & 60.41 & 56.93 & 38.44 & 70.78 & 38.44 & 40.51 & 37.08 \\
YV & 56.29 & 84.67 & 65.21 & 57.03 & 55.55 & 36.40 & 68.24 & 36.40 & 38.47 & 35.04 \\
YL & 55.71 & 84.09 & 64.63 & 56.45 & 54.97 & 35.82 & 67.66 & 35.82 & 37.89 & 34.46 \\
QI & 56.85 & 85.23 & 65.77 & 57.59 & 56.11 & 37.56 & 69.40 & 37.56 & 39.63 & 36.20 \\
MR & 57.41 & 85.79 & 66.33 & 58.15 & 56.67 & 38.12 & 70.46 & 38.12 & 40.19 & 36.76 \\
SL & 54.13 & 82.51 & 63.05 & 54.87 & 53.39 & 34.24 & 66.08 & 34.24 & 36.31 & 32.88 \\
DT & 59.23 & 87.61 & 68.15 & 60.97 & 57.49 & 39.00 & 71.34 & 39.00 & 41.07 & 37.64 \\
MRT & 58.95 & 87.33 & 67.87 & 60.69 & 57.21 & 38.72 & 71.06 & 38.72 & 40.79 & 37.36 \\
TCD & 56.57 & 84.95 & 65.49 & 57.31 & 55.83 & 37.28 & 69.12 & 37.28 & 39.35 & 35.92 \\
CSAF & 61.19 & 89.57 & 70.11 & 62.93 & 59.45 & 42.29 & 76.69 & 44.11 & 44.52 & 40.95 \\
\hline
\end{tabular}
\end{table}

\subsection{单一森林跨物候期和物种的树冠计数能力}

在RRA-Set数据集上进行的计数实验显示，CSAF在不同季节的树木计数任务中表现出高度稳定性，年平均准确率达到91.16\%，显著高于DT模型的63.75\%。特别是在2月的落叶期，当树冠的光谱特征发生显著变化时，CSAF保持92\%的计数准确率，而DT的准确率下降至57\%。

在更复杂的RA-Set数据集上，CSAF达到97.88\%的平均计数准确率，显著优于DT模型的82.33\%。全年结果显示，CSAF在整个年度计数任务中保持一致的高稳定性，而DT在准确率上表现出相当大的波动。

\subsection{混交林跨物种的树冠分割和计数}

\subsubsection{跨物种城市森林和混交林的树冠分割效果}

UT-Set数据集呈现了小目标尺寸、复杂背景干扰和多物种共存导致的显著特征变异等挑战。尽管存在这些复杂性，CSAF仍表现出卓越的分割性能。如表\ref{tab:mixed_forest}所示，CSAF在所有评估指标上始终优于所有竞争模型。

在CTF-Set数据集上进行的跨物种分割实验中，该数据集包含12个植物科的24个异质树种，树冠形态差异显著。直接应用在RT-Set上训练的模型权重而不进行任何额外微调，CSAF仍保持优越的分割性能，甚至超过了其在RT-Set上的结果。

\begin{table}[htbp]
\centering
\caption{CSAF与基线模型在UT-Set和CTF-Set数据集上的性能比较}
\label{tab:mixed_forest}
\begin{tabular}{l|ccccc|ccccc}
\hline
\multirow{2}{*}{模型} & \multicolumn{5}{c|}{UT-Set} & \multicolumn{5}{c}{CTF-Set} \\
& AP & AP50 & AP75 & APm & APl & AP & AP50 & AP75 & APm & APl \\
\hline
CMR & 43.01 & 74.79 & 55.80 & 45.58 & 58.44 & 29.3 & 67.8 & 24.6 & 29.5 & 29.5 \\
SW & 40.41 & 73.79 & 50.91 & 43.95 & 46.31 & 27.1 & 59.4 & 24.3 & 27.3 & 27.3 \\
HTC & 42.33 & 70.83 & 48.99 & 44.12 & 58.67 & 22.00 & 53.5 & 16.3 & 22.3 & 22.3 \\
YV & 33.04 & 69.74 & 29.93 & 36.94 & 34.03 & 18.3 & 48.9 & 11.0 & 18.4 & 18.4 \\
YL & 31.61 & 65.41 & 21.62 & 37.80 & 26.24 & 22.1 & 53.0 & 15.3 & 22.2 & 22.2 \\
QI & 33.69 & 66.25 & 29.29 & 39.26 & 40.66 & 13.8 & 43.8 & 6.2 & 13.9 & 13.9 \\
MR & 34.83 & 68.41 & 36.24 & 41.70 & 29.61 & 30.2 & 63.9 & 24.1 & 30.1 & 30.1 \\
SL & 27.42 & 60.81 & 20.97 & 31.04 & 40.19 & 11.4 & 39.6 & 3.1 & 11.4 & 11.4 \\
DT & 43.41 & 68.54 & 51.18 & 49.63 & 58.37 & 31.55 & 69.67 & 28.77 & 31.54 & 31.54 \\
MRT & 43.37 & 67.53 & 50.60 & 47.86 & 59.18 & 25.65 & 65.68 & 24.03 & 28.94 & 28.94 \\
TCD & 32.93 & 66.09 & 38.50 & 32.54 & 41.54 & 23.00 & 60.44 & 18.68 & 23.00 & 23.00 \\
CSAF & 48.31 & 77.03 & 59.66 & 51.95 & 63.32 & 35.43 & 76.71 & 27.57 & 35.48 & 35.48 \\
\hline
\end{tabular}
\end{table}

\subsubsection{混交林跨物种的树冠计数泛化性能}

在混交林的树冠计数任务中，CSAF在所有三个区域都保持一致的高计数准确率。相比之下，DT在不同区域的计数稳定性表现出更大的变异性。例如，在树冠相对较大的区域3，DT的准确率下降至77\%，而CSAF保持在92\%的稳定水平，展现出优越的鲁棒性。

\subsection{灾后应用}

2024年9月6日，海南省遭受超强台风"摩羯"直接袭击，风速超过62 m/s，最低中心气压915 hPa。选择受损最严重的区域进行灾前灾后植被动态定量研究。

\subsubsection{受灾树木计数}

与稳定环境条件下的计数实验相比，进一步验证了CSAF模型在灾后条件下的鲁棒性和准确性。全年平均准确率达到92.46\%，表明CSAF模型不仅在稳定、结构良好的植被环境中实现高精度计数，在植被结构被超强台风严重破坏的场景下也保持强适应性。

\subsubsection{灾前灾后监测条件分析}

在台风后植被健康评估中，系统比较了SCVI方法与传统SIC方法的性能差异。SCVI方法有效最小化了土壤背景等非植被因子的干扰，提供了植被本身光谱特征的更准确表征。例如，对于ExG指数，使用SCVI得到的值为0.5413，而受背景干扰影响的SIC方法得到的值为0.4587，差异为0.0826，相对误差为15.26\%。

在灾后响应敏感性方面，SCVI同样表现出明显优势。例如，在MGRVI指数的情况下，SCVI检测到从0.7774增加到0.8779，反映了12.9\%的上升，而SIC方法显示从0.9955轻微下降到0.9664，对应2.9\%的减少。

长期时间监测观察显示，除了反映红光和蓝光波段强度的ExR和ExB等指数外，大多数主要基于绿光波段的植被指数在SCVI计算下表现出普遍的"下降后恢复"趋势，这与灾后预期的生态恢复过程一致。相比之下，使用SIC方法计算的几个指数（如VARI、VGI）显示出异常的"先增后减"模式，表明在捕获灾后非稳态植被变化方面存在潜在偏差。

\subsection{分割误差对比分析}

为确保树冠计数结果的生态可解释性和实用性，必须在有效控制分割误差的同时实现个体树冠的精确划分，特别是在物种组成多样化和物候期变化的生态异质性条件下。根据已有研究的误差分类方法，将常见分割误差分为三种主要类型：(1) 漏检树冠，(2) 树冠合并或破碎，(3) 错误植被分割。

在三个代表性数据集（RRA-Set、RA-Set和CTF-Set）上的定量比较显示，CSAF和DT模型在不同生态和物候条件下的分割误差类型分布存在显著差异。在RRA-Set中，季节变化显著影响了模型识别树冠边界的能力。9月和10月观察到的过分割和欠分割与冠层密度增加和树冠轮廓模糊密切相关。尽管CSAF和DT都受到季节变化的影响，但CSAF始终表现出更稳定的误差控制，特别是在处理合并和破碎树冠案例方面。

在RA-Set数据集中，非目标植被的结构不规则性为MPC-Poisson模块提供了更清晰的几何线索，使CSAF在抑制背景干扰方面优于基线模型DT。在CTF-Set数据集的跨物种评估中，分割误差的分布表现出不同的模式。在这种异质森林环境中，模型误差主要源于对种间形态变异性的适应不足以及对非树冠区域（如湖泊边缘或裸露岩石表面）的误分类。

\subsection{时空、生态和成像域的性能分析}

实验结果表明，CSAF在多个时空尺度、不同生态类型和变化成像模式下，相比于DT、MRT和TCD等已建立的树冠分割模型，表现出优越的稳定性和泛化能力。虽然这些基线模型在结构简单的数据集（如BT-Set）上达到高性能，但在更复杂和异质的数据集（如RT-Set、UT-Set和CTF-Set）上效果显著下降。

这种性能差距主要归因于CSAF框架对物候期和物种特异性特征动态的显式建模、捕获长程空间依赖关系的能力，以及对树冠形态物理基础约束的集成。相比之下，DT、MRT和TCD等模型在设计上缺乏感知复杂生态结构和物候变异性的机制，因此在环境异质性增加时往往表现出显著的性能退化。

\subsection{SCVI方法在灾后遥感监测中的优势}

研究发现，传统光谱指数计算方法（SIC）在灾后遥感应用中经常失效。这种局限性主要不是源于指数本身的数学公式，而是源于它们无法有效分离树冠与背景元素。传统方法通常在整个图像或粗糙空间区域上计算植被指数，使其极易受到裸露土壤、倒伏枝条和其他非植被成分的背景干扰。

这种干扰引入了显著的光谱污染，扭曲了植被指数的时间响应模式。实验结果证实了这一现象，特别是对于某些基于绿色光谱波段的指数。在早期干扰阶段，SIC方法经常表现出异常的"先增后减"趋势，这与实际植被退化的生理轨迹存在显著偏差。

相比之下，SCVI方法基于实例级分割输出计算植被指数，并结合了抑制非目标背景干扰的有效机制。因此，它显著增强了植被指数在捕获真实生态变化方面的敏感性和可靠性。具体而言，对于依赖绿色光谱波段的指数，SCVI表现出以初始下降后逐渐恢复为特征的自然退化-恢复曲线。对于基于红色或红边波段的指数，SCVI准确捕获了早期增加后下降的动态轨迹，两者都反映了生态一致的模式。

从方法论角度来看，这些发现突出了SCVI实例引导植被指数计算框架在应对复杂遥感环境挑战方面的广泛潜力。特别是在灾后生态监测、物候响应检测和混合植被识别等任务中，SCVI利用包含区域评估、个体树木定位和像素级分析的三级结构，能够更准确地表征植被生理状态，促进构建连接宏观到微观尺度观测的综合生态监测系统。

\section{Conclusion}

本研究提出了一个基于深度学习的橡胶树冠分割与分析框架，通过集成GM-Mamba、MASA-Optimizer和MPC-Poisson三个专门化模块，有效解决了跨物候期和跨物种条件下树冠分割的关键挑战。

GM-Mamba模块通过结合傅里叶变换频域去噪、拉普拉斯金字塔多尺度边缘增强和Mamba状态空间模型的长程依赖建模，显著提升了模糊树冠边界的识别能力。该模块将AP50指标提升了2.07\%，有效解决了密集森林中树冠边界模糊的问题。

MASA-Optimizer模块通过多智能体协作的自适应遗忘因子优化机制，成功解决了跨物候期训练中的灾难性遗忘问题。该模块维护容量为100的MemoryBuffer，通过模拟退火、强化学习和遗传算法的三阶段优化策略，将AP50指标提升了2.29\%，显著增强了模型在多时相数据上的泛化能力。

MPC-Poisson模块基于物理信息神经网络的思想，将泊松方程的物理约束嵌入到神经网络中，通过约束分割结果的空间连贯性和形态合理性，有效抑制了杂草等非目标植被的干扰。该模块将AP50指标提升了1.49\%，提高了模型对不规则形状植被的抑制能力。

三个模块的协同集成使完整的CSAF框架在AP50指标上达到76.63\%，相比基线模型提升了6.11\%。在多个数据集上的广泛实验验证了框架的有效性：在单一森林环境下，CSAF在年度计数任务中达到91.16\%的平均准确率，显著优于对比模型的63.75\%；在混交林环境下，CSAF在跨物种分割任务中保持优越性能，即使在包含24个树种的复杂生态环境中也能维持稳定的分割精度。

此外，本研究提出的SCVI方法通过基于实例级分割的植被指数计算，有效解决了传统SIC方法在灾后监测中的背景干扰问题。在台风灾害应用中，SCVI方法相比SIC方法在ExG指数计算上减少了15.26\%的相对误差，更准确地反映了植被的真实生理状态和恢复过程。

实验结果表明，所提出的框架在处理复杂生态环境、极端气候条件和长期时序监测方面具有显著优势，为橡胶树种植园的精准管理和生态监测提供了有效的技术支撑。未来工作将重点关注进一步提升模型对形态相似非目标植被的抑制能力，以及增强密集植被区域中个体树冠的长期一致性跟踪能力。

\section*{Acknowledgment}

The authors would like to thank the anonymous reviewers for their valuable comments and suggestions.

% references section
\bibliographystyle{IEEEtran}
\bibliography{ieee_tgrs_references}

\begin{IEEEbiography}{Firstname Lastname}
received the B.S. degree in remote sensing from University A in 2015, and the Ph.D. degree in geoscience and remote sensing from University B in 2020. He is currently a Research Scientist with the Institute of Remote Sensing. His research interests include deep learning, plant phenotyping, and precision agriculture.
\end{IEEEbiography}

\begin{IEEEbiography}{Secondname Lastname}
received the Ph.D. degree in computer science from University C in 2018. She is currently an Associate Professor with the Department of Remote Sensing. Her research focuses on machine learning applications in agriculture and environmental monitoring.
\end{IEEEbiography}

\begin{IEEEbiography}{Thirdname Lastname}
is a Professor and Director of the Remote Sensing Laboratory. His research interests include hyperspectral remote sensing, crop monitoring, and precision agriculture technologies.
\end{IEEEbiography}

\end{document}
