\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand*\HyPL@Entry[1]{}
\citation{priyadarshan2017biology}
\citation{ke2011review}
\citation{gougeon1998automatic}
\citation{culvenor2002tida,erikson2003segmentation}
\citation{jing2012automated}
\citation{yang2014automated}
\citation{wang2004automated,wang2010crown}
\citation{lamar2005automated}
\citation{tong2021improved}
\citation{zhao2023review}
\citation{lassalle2022cnn}
\citation{freudenberg2022individual}
\citation{ronneberger2015u}
\citation{he2017mask}
\citation{braga2020amazon,hao2021individual,ball2023detectree2}
\HyPL@Entry{0<</S/D>>}
\@writefile{toc}{\contentsline {section}{\numberline {I}引言}{1}{section.1}\protected@file@percent }
\citation{priyadarshan2017biology}
\@writefile{toc}{\contentsline {section}{\numberline {II}材料与方法}{2}{section.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-A}}研究区域}{2}{subsection.2.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-B}}数据集构建}{2}{subsection.2.2}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces 研究区域位置图。}}{2}{figure.1}\protected@file@percent }
\newlabel{fig:study_area}{{1}{2}{研究区域位置图。}{figure.1}{}}
\@writefile{toc}{\contentsline {section}{\numberline {III}Methodology}{2}{section.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-A}}总体架构}{2}{subsection.3.1}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {I}{\ignorespaces 实验数据集详细信息}}{3}{table.1}\protected@file@percent }
\newlabel{tab:datasets}{{I}{3}{实验数据集详细信息}{table.1}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces CSAF框架总体架构图。(A) 预处理阶段包括图像裁剪、亮度对比度调整和骨干网络特征提取；(B) GM-Mamba模块通过多尺度梯度编码和空间建模增强树冠边界；(C) MASA-Optimizer采用多智能体学习策略解决跨物种和物候期的灾难性遗忘问题；(D) MPC-Poisson通过基于泊松方程的约束嵌入形状感知先验，抑制背景干扰；(E) 输出包括精确的树冠掩膜、植被指数热图(SCVI)和树级数据分析。}}{3}{figure.2}\protected@file@percent }
\newlabel{fig:framework}{{2}{3}{CSAF框架总体架构图。(A) 预处理阶段包括图像裁剪、亮度对比度调整和骨干网络特征提取；(B) GM-Mamba模块通过多尺度梯度编码和空间建模增强树冠边界；(C) MASA-Optimizer采用多智能体学习策略解决跨物种和物候期的灾难性遗忘问题；(D) MPC-Poisson通过基于泊松方程的约束嵌入形状感知先验，抑制背景干扰；(E) 输出包括精确的树冠掩膜、植被指数热图(SCVI)和树级数据分析。}{figure.2}{}}
\@writefile{lot}{\contentsline {table}{\numberline {II}{\ignorespaces 橡胶树数据集物候期分布}}{3}{table.2}\protected@file@percent }
\newlabel{tab:phenology}{{II}{3}{橡胶树数据集物候期分布}{table.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-B}}GM-Mamba边界增强模块}{3}{subsection.3.2}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces GM-Mamba模块架构图。该模块集成了基于傅里叶变换的频域去噪、基于拉普拉斯金字塔的多尺度边缘增强，以及基于Mamba的长程依赖建模。从频域的实部和虚部提取双尺度梯度线索，实现从粗到细的边缘检测。输出传入选择性扫描增强的SSM，通过动态状态更新捕获方向性树冠结构。融合输出增强边界连续性和树冠级空间一致性。}}{3}{figure.3}\protected@file@percent }
\newlabel{fig:gm_mamba}{{3}{3}{GM-Mamba模块架构图。该模块集成了基于傅里叶变换的频域去噪、基于拉普拉斯金字塔的多尺度边缘增强，以及基于Mamba的长程依赖建模。从频域的实部和虚部提取双尺度梯度线索，实现从粗到细的边缘检测。输出传入选择性扫描增强的SSM，通过动态状态更新捕获方向性树冠结构。融合输出增强边界连续性和树冠级空间一致性。}{figure.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-C}}MPC-Poisson物理约束模块}{4}{subsection.3.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces MPC-Poisson模块架构图。该模块通过四层MLP将归一化空间坐标映射为树冠存在的概率场$\gamma (x,y)$。基于泊松方程导出的物理信息损失项惩罚偏离预期扩散模式的情况，从而抑制不规则的非目标植被并增强树冠掩膜的连贯性。}}{4}{figure.4}\protected@file@percent }
\newlabel{fig:mpc_poisson}{{4}{4}{MPC-Poisson模块架构图。该模块通过四层MLP将归一化空间坐标映射为树冠存在的概率场$\gamma (x,y)$。基于泊松方程导出的物理信息损失项惩罚偏离预期扩散模式的情况，从而抑制不规则的非目标植被并增强树冠掩膜的连贯性。}{figure.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-D}}MASA-Optimizer持续学习机制}{4}{subsection.3.4}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {5}{\ignorespaces MASA-Optimizer框架概览图。该模块在持续学习中集成模拟退火、强化学习和遗传搜索三个优化阶段，动态调整遗忘因子$\alpha $，从而在历史和新颖特征表示之间保持平衡。经验回放和自适应更新使得在物种和物候变异性条件下能够鲁棒融合多时相冠层特征。}}{4}{figure.5}\protected@file@percent }
\newlabel{fig:masa_optimizer}{{5}{4}{MASA-Optimizer框架概览图。该模块在持续学习中集成模拟退火、强化学习和遗传搜索三个优化阶段，动态调整遗忘因子$\alpha $，从而在历史和新颖特征表示之间保持平衡。经验回放和自适应更新使得在物种和物候变异性条件下能够鲁棒融合多时相冠层特征。}{figure.5}{}}
\bibstyle{IEEEtran}
\bibdata{ieee_tgrs_references}
\bibcite{priyadarshan2017biology}{1}
\bibcite{ke2011review}{2}
\bibcite{gougeon1998automatic}{3}
\bibcite{culvenor2002tida}{4}
\bibcite{erikson2003segmentation}{5}
\bibcite{jing2012automated}{6}
\bibcite{yang2014automated}{7}
\bibcite{wang2004automated}{8}
\bibcite{wang2010crown}{9}
\bibcite{lamar2005automated}{10}
\bibcite{tong2021improved}{11}
\bibcite{zhao2023review}{12}
\bibcite{lassalle2022cnn}{13}
\bibcite{freudenberg2022individual}{14}
\bibcite{ronneberger2015u}{15}
\bibcite{he2017mask}{16}
\bibcite{braga2020amazon}{17}
\bibcite{hao2021individual}{18}
\bibcite{ball2023detectree2}{19}
\@writefile{toc}{\contentsline {section}{\numberline {IV}Experimental Setup}{5}{section.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{References}{5}{section*.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{Biographies}{6}{IEEEbiography.0}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{Firstname Lastname}{6}{IEEEbiography.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{Secondname Lastname}{6}{IEEEbiography.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{Thirdname Lastname}{6}{IEEEbiography.3}\protected@file@percent }
\gdef \@abspage@last{6}
