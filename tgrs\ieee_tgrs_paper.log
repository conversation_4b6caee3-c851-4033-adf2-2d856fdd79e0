This is XeTeX, Version 3.141592653-2.6-0.999995 (MiKTeX 24.1) (preloaded format=xelatex 2025.8.1)  3 AUG 2025 19:05
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex
(e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(C:\Program Files\MiKTeX\tex/latex/ieeetran\IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count183
\@IEEEtrantmpcountB=\count184
\@IEEEtrantmpcountC=\count185
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 503.
LaTeX Font Info:    No file TUptm.fd. on input line 503.

LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 503.

-- Using 8.5in x 11in (letter) paper.
-- Using DVI output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146

LaTeX Font Warning: Font shape `TU/ptm/bx/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/bx/it' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 1090.

\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@IEEEsubequation=\count190
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count191
\c@table=\count192
\@IEEEeqnnumcols=\count193
\@IEEEeqncolcnt=\count194
\@IEEEsubeqnnumrollback=\count195
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count196
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count197
\@IEEEtranrubishbin=\box52
) (C:\Program Files\MiKTeX\tex/latex/cite\cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (C:\Program Files\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (C:\Program Files\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks18
) (C:\Program Files\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (C:\Program Files\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (C:\Program Files\MiKTeX\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
 (C:\Program Files\MiKTeX\tex/latex/graphics-def\xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen164
\Gin@req@width=\dimen165
) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(C:\Program Files\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (C:\Program Files\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen166
)) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen167
) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count198
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count199
\leftroot@=\count266
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count267
\DOTSCASE@=\count268
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen168
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count269
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count270
\dotsspace@=\muskip16
\c@parentequation=\count271
\dspbrk@lvl=\count272
\tag@help=\toks20
\row@=\count273
\column@=\count274
\maxfields@=\count275
\andhelp@=\toks21
\eqnshift@=\dimen169
\alignsep@=\dimen170
\tagshift@=\dimen171
\tagwidth@=\dimen172
\totwidth@=\dimen173
\lineht@=\dimen174
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (C:\Program Files\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (C:\Program Files\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (C:\Program Files\MiKTeX\tex/latex/algorithms\algorithm.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (C:\Program Files\MiKTeX\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count276
\float@exts=\toks24
\float@box=\box55
\@float@everytoks=\toks25
\@floatcapt=\box56
) (C:\Program Files\MiKTeX\tex/latex/base\ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks26
\c@algorithm=\count277
) (C:\Program Files\MiKTeX\tex/latex/algorithms\algorithmic.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
\c@ALC@unique=\count278
\c@ALC@line=\count279
\c@ALC@rem=\count280
\c@ALC@depth=\count281
\ALC@tlm=\skip54
\algorithmicindent=\skip55
) (C:\Program Files\MiKTeX\tex/latex/tools\array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen175
\ar@mcellbox=\box57
\extrarowheight=\dimen176
\NC@list=\toks27
\extratabsurround=\skip56
\backup@length=\skip57
\ar@cellbox=\box58
) (C:\Program Files\MiKTeX\tex/latex/mdwtools\mdwmath.sty
Package: mdwmath 1996/04/11 1.1 Nice mathematical things
\sq@sqrt=\count282
LaTeX Info: Redefining \sqrt on input line 84.
) (C:\Program Files\MiKTeX\tex/latex/mdwtools\mdwtab.sty
Package: mdwtab 1998/04/28 1.9 Table typesetting with style
\tab@state=\count283
\tab@columns=\count284
\tab@preamble=\toks28
\tab@shortline=\toks29
\extrarowheight=\dimen177
\tabextrasep=\dimen178
\arrayextrasep=\dimen179
\smarraycolsep=\dimen180
\smarrayextrasep=\dimen181
\tab@width=\dimen182
\col@sep=\dimen183
\tab@endheight=\dimen184
\tab@leftskip=\skip58
\tab@rightskip=\skip59
\fn@notes=\box59
\fn@width=\dimen185
) (C:\Program Files\MiKTeX\tex/latex/eqparbox\eqparbox.sty
Package: eqparbox 2017/09/03 v4.1 Create equal-widthed boxes
\eqp@tempdima=\skip60
\eqp@tempdimb=\skip61
\eqp@tabular@box=\box60
\eqp@list@box=\box61
\eqp@list@indent=\skip62
 (C:\Program Files\MiKTeX\tex/latex/environ\environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments
 (C:\Program Files\MiKTeX\tex/latex/trimspaces\trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
))) (C:\Program Files\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (C:\Program Files\MiKTeX\tex/latex/subfigure\subfigure.sty
Package: subfigure 2002/07/30 v2.1.4 subfigure package
\subfigtopskip=\skip63
\subfigcapskip=\skip64
\subfigcaptopadj=\dimen186
\subfigbottomskip=\skip65
\subfigcapmargin=\dimen187
\subfiglabelskip=\skip66
\c@subfigure=\count285
\c@lofdepth=\count286
\c@subtable=\count287
\c@lotdepth=\count288

****************************************
* Local config file subfigure.cfg used *
****************************************
(C:\Program Files\MiKTeX\tex/latex/subfigure\subfigure.cfg)
\subfig@top=\skip67
\subfig@bottom=\skip68
) (C:\Program Files\MiKTeX\tex/latex/multirow\multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip69
\multirow@cntb=\count289
\multirow@dima=\skip70
\bigstrutjot=\dimen188
) (C:\Program Files\MiKTeX\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen189
\lightrulewidth=\dimen190
\cmidrulewidth=\dimen191
\belowrulesep=\dimen192
\belowbottomsep=\dimen193
\aboverulesep=\dimen194
\abovetopsep=\dimen195
\cmidrulesep=\dimen196
\cmidrulekern=\dimen197
\defaultaddspace=\dimen198
\@cmidla=\count290
\@cmidlb=\count291
\@aboverulesep=\dimen199
\@belowrulesep=\dimen256
\@thisruleclass=\count292
\@lastruleclass=\count293
\@thisrulewidth=\dimen257
) (C:\Program Files\MiKTeX\tex/latex/graphics\color.sty
Package: color 2022/01/06 v1.3d Standard LaTeX Color (DPC)
 (C:\Program Files\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: xetex.def on input line 149.
 (C:\Program Files\MiKTeX\tex/latex/graphics\mathcolor.ltx)) (C:\Program Files\MiKTeX\tex/latex/preprint\balance.sty
Package: balance 1999/02/23 4.3 (PWD)
\oldvsize=\dimen258
) (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec.sty (C:\Program Files\MiKTeX\tex/latex/l3packages/xparse\xparse.sty (C:\Program Files\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2024-01-04 L3 programming layer (loader) 
 (C:\Program Files\MiKTeX\tex/latex/l3backend\l3backend-xetex.def
File: l3backend-xetex.def 2024-01-04 L3 backend support: XeTeX
\g__graphics_track_int=\count294
\l__pdf_internal_box=\box62
\g__pdf_backend_object_int=\count295
\g__pdf_backend_annotation_int=\count296
\g__pdf_backend_link_int=\count297
))
Package: xparse 2023-10-10 L3 Experimental document command parser
)
Package: fontspec 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
 (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec-xetex.sty
Package: fontspec-xetex 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count298
\l__fontspec_language_int=\count299
\l__fontspec_strnum_int=\count300
\l__fontspec_tmp_int=\count301
\l__fontspec_tmpa_int=\count302
\l__fontspec_tmpb_int=\count303
\l__fontspec_tmpc_int=\count304
\l__fontspec_em_int=\count305
\l__fontspec_emdef_int=\count306
\l__fontspec_strong_int=\count307
\l__fontspec_strongdef_int=\count308
\l__fontspec_tmpa_dim=\dimen259
\l__fontspec_tmpb_dim=\dimen260
\l__fontspec_tmpc_dim=\dimen261
 (C:\Program Files\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec.cfg))) (C:\Program Files\MiKTeX\tex/xelatex/xecjk\xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX
 (C:\Program Files\MiKTeX\tex/latex/ctex\ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (C:\Program Files\MiKTeX\tex/latex/l3packages/xtemplate\xtemplate.sty
Package: xtemplate 2023-10-10 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen262
\l__xtemplate_tmp_int=\count309
\l__xtemplate_tmp_muskip=\muskip18
\l__xtemplate_tmp_skip=\skip71
)
\l__xeCJK_tmp_int=\count310
\l__xeCJK_tmp_box=\box63
\l__xeCJK_tmp_dim=\dimen263
\l__xeCJK_tmp_skip=\skip72
\g__xeCJK_space_factor_int=\count311
\l__xeCJK_begin_int=\count312
\l__xeCJK_end_int=\count313
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip73
\c__xeCJK_none_node=\count314
\g__xeCJK_node_int=\count315
\c__xeCJK_CJK_node_dim=\dimen264
\c__xeCJK_CJK-space_node_dim=\dimen265
\c__xeCJK_default_node_dim=\dimen266
\c__xeCJK_CJK-widow_node_dim=\dimen267
\c__xeCJK_normalspace_node_dim=\dimen268
\c__xeCJK_default-space_node_skip=\skip74
\l__xeCJK_ccglue_skip=\skip75
\l__xeCJK_ecglue_skip=\skip76
\l__xeCJK_punct_kern_skip=\skip77
\l__xeCJK_indent_box=\box64
\l__xeCJK_last_penalty_int=\count316
\l__xeCJK_last_bound_dim=\dimen269
\l__xeCJK_last_kern_dim=\dimen270
\l__xeCJK_widow_penalty_int=\count317

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen271
\l__xeCJK_mixed_punct_width_dim=\dimen272
\l__xeCJK_middle_punct_width_dim=\dimen273
\l__xeCJK_fixed_margin_width_dim=\dimen274
\l__xeCJK_mixed_margin_width_dim=\dimen275
\l__xeCJK_middle_margin_width_dim=\dimen276
\l__xeCJK_bound_punct_width_dim=\dimen277
\l__xeCJK_bound_margin_width_dim=\dimen278
\l__xeCJK_margin_minimum_dim=\dimen279
\l__xeCJK_kerning_total_width_dim=\dimen280
\l__xeCJK_same_align_margin_dim=\dimen281
\l__xeCJK_different_align_margin_dim=\dimen282
\l__xeCJK_kerning_margin_width_dim=\dimen283
\l__xeCJK_kerning_margin_minimum_dim=\dimen284
\l__xeCJK_bound_dim=\dimen285
\l__xeCJK_reverse_bound_dim=\dimen286
\l__xeCJK_margin_dim=\dimen287
\l__xeCJK_minimum_bound_dim=\dimen288
\l__xeCJK_kerning_margin_dim=\dimen289
\g__xeCJK_family_int=\count318
\l__xeCJK_fam_int=\count319
\g__xeCJK_fam_allocation_int=\count320
\l__xeCJK_verb_case_int=\count321
\l__xeCJK_verb_exspace_skip=\skip78
 (C:\Program Files\MiKTeX\tex/xelatex/xecjk\xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
)) (C:\Program Files\MiKTeX\tex/latex/algorithm2e\algorithm2e.sty
Invalid UTF-8 byte or sequence at line 284 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 299 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 550 replaced by U+FFFD.
Package: algorithm2e 2017/07/18 v5.2 algorithms environments
\c@AlgoLine=\count322
\algocf@hangindent=\skip79
 (C:\Program Files\MiKTeX\tex/latex/ifoddpage\ifoddpage.sty
Package: ifoddpage 2022/10/18 v1.2 Conditionals for odd/even page detection
\c@checkoddpage=\count323
) (C:\Program Files\MiKTeX\tex/latex/tools\xspace.sty
Package: xspace 2014/10/28 v1.13 Space after command names (DPC,MH)
) (C:\Program Files\MiKTeX\tex/latex/relsize\relsize.sty
Package: relsize 2013/03/29 ver 4.1
)
\skiptotal=\skip80
\skiplinenumber=\skip81
\skiprule=\skip82
\skiphlne=\skip83
\skiptext=\skip84
\skiplength=\skip85
\algomargin=\skip86
\skipalgocfslide=\skip87
\algowidth=\dimen290
\inoutsize=\dimen291
\inoutindent=\dimen292
\interspacetitleruled=\dimen293
\interspacealgoruled=\dimen294
\interspacetitleboxruled=\dimen295
\algocf@ruledwidth=\skip88
\algocf@inoutbox=\box65
\algocf@inputbox=\box66
\AlCapSkip=\skip89
\AlCapHSkip=\skip90
\algoskipindent=\skip91
\algocf@nlbox=\box67
\algocf@hangingbox=\box68
\algocf@untilbox=\box69
\algocf@skipuntil=\skip92
\algocf@capbox=\box70
\algocf@lcaptionbox=\skip93
\algoheightruledefault=\skip94
\algoheightrule=\skip95
\algotitleheightruledefault=\skip96
\algotitleheightrule=\skip97
\c@algocfline=\count324
\c@algocfproc=\count325
\c@algocf=\count326
\algocf@algoframe=\box71
\algocf@algobox=\box72


C:\Program Files\MiKTeX\tex/latex/algorithm2e\algorithm2e.sty:2746: LaTeX Error: Command \algorithm already defined.
               Or name \end... illegal, see p.192 of the manual.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.2746 }
        %
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


C:\Program Files\MiKTeX\tex/latex/algorithm2e\algorithm2e.sty:2754: LaTeX Error: Command \algorithm* already defined.
               Or name \end... illegal, see p.192 of the manual.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.2754 }
        %
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


C:\Program Files\MiKTeX\tex/latex/algorithm2e\algorithm2e.sty:2761: LaTeX Error: Command \listofalgorithms already defined.
               Or name \end... illegal, see p.192 of the manual.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.2761 }
        %
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

) (C:\Program Files\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2023-11-26 v7.01g Hypertext links for LaTeX
 (C:\Program Files\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
) (C:\Program Files\MiKTeX\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (C:\Program Files\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (C:\Program Files\MiKTeX\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (C:\Program Files\MiKTeX\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (C:\Program Files\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (C:\Program Files\MiKTeX\tex/generic/pdftexcmds\pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (C:\Program Files\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (C:\Program Files\MiKTeX\tex/latex/letltxmacro\letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
) (C:\Program Files\MiKTeX\tex/latex/auxhook\auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (C:\Program Files\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (C:\Program Files\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (C:\Program Files\MiKTeX\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (C:\Program Files\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count327
) (C:\Program Files\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count328
)
\@linkdim=\dimen296
\Hy@linkcounter=\count329
\Hy@pagecounter=\count330
 (C:\Program Files\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2023-11-26 v7.01g Hyperref: PDFDocEncoding definition (HO)
) (C:\Program Files\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count331
 (C:\Program Files\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2023-11-26 v7.01g Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4064.
Package hyperref Info: Option `bookmarks' set `false' on input line 4064.
Package hyperref Info: Hyper figures OFF on input line 4181.
Package hyperref Info: Link nesting OFF on input line 4186.
Package hyperref Info: Hyper index ON on input line 4189.
Package hyperref Info: Plain pages OFF on input line 4196.
Package hyperref Info: Backreferencing OFF on input line 4201.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks OFF on input line 4454.
\c@Hy@tempcnt=\count332
LaTeX Info: Redefining \url on input line 4786.
\XeTeXLinkMargin=\dimen297
 (C:\Program Files\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (C:\Program Files\MiKTeX\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count333
\Field@Width=\dimen298
\Fld@charsize=\dimen299
Package hyperref Info: Hyper figures OFF on input line 6065.
Package hyperref Info: Link nesting OFF on input line 6070.
Package hyperref Info: Hyper index ON on input line 6073.
Package hyperref Info: backreferencing OFF on input line 6080.
Package hyperref Info: Link coloring ON on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6090.
Package hyperref Info: PDF/A mode OFF on input line 6095.
 (C:\Program Files\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count334
\c@Item=\count335
\c@Hfootnote=\count336
)
Package hyperref Info: Driver: hxetex.
 (C:\Program Files\MiKTeX\tex/latex/hyperref\hxetex.def
File: hxetex.def 2023-11-26 v7.01g Hyperref driver for XeTeX
 (C:\Program Files\MiKTeX\tex/generic/stringenc\stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box73
\c@Hy@AnnotLevel=\count337
\HyField@AnnotCount=\count338
\Fld@listcount=\count339
\c@bookmark@seq@number=\count340
 (C:\Program Files\MiKTeX\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)
 (C:\Program Files\MiKTeX\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 284.
)
\Hy@SectionHShift=\skip98
)

Package xeCJK Warning: Fandol is being set as the default font for CJK text.
(xeCJK)                Please make sure it has been properly installed.


Package fontspec Warning: Font "FandolSong-Regular" does not contain requested
(fontspec)                Script "CJK".


Package fontspec Info: Font family 'FandolSong-Regular(0)' created for font
(fontspec)             'FandolSong-Regular' with options
(fontspec)             [Script={CJK},Extension={.otf},BoldFont={FandolSong-Bold},ItalicFont={FandolKai-Regular}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[FandolSong-Regular.otf]/OT:language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"[FandolSong-Bold.otf]/OT:language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"[FandolKai-Regular.otf]/OT:language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 

(ieee_tgrs_paper.aux)
\openout1 = `ieee_tgrs_paper.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 31.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 31.
 (C:\Program Files\MiKTeX\tex/latex/base\ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.

-- Lines per column: 58 (exact).

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 31.
LaTeX Font Info:    Redeclaring math accent \acute on input line 31.
LaTeX Font Info:    Redeclaring math accent \grave on input line 31.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 31.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 31.
LaTeX Font Info:    Redeclaring math accent \bar on input line 31.
LaTeX Font Info:    Redeclaring math accent \breve on input line 31.
LaTeX Font Info:    Redeclaring math accent \check on input line 31.
LaTeX Font Info:    Redeclaring math accent \hat on input line 31.
LaTeX Font Info:    Redeclaring math accent \dot on input line 31.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 31.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 31.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 31.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 31.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 31.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 31.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 31.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 31.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 31.
Package hyperref Info: Link coloring ON on input line 31.

Package hyperref Warning: Rerun to get /PageLabels entry.


Underfull \hbox (badness 3679) in paragraph at lines 47--47
\TU/lmr/m/n/8 versity of Agriculture, City, State 12345 USA (e-mail: first-
 []


Underfull \hbox (badness 7613) in paragraph at lines 47--47
[][][]\TU/lmr/m/n/8 T. Lastname is with the Department of Computer Sci-
 []


Underfull \hbox (badness 3884) in paragraph at lines 47--47
\TU/lmr/m/n/8 ence, Technology University, City, State 12345 USA (e-mail:
 []


LaTeX Font Warning: Font shape `TU/FandolSong-Regular(0)/m/sc' undefined
(Font)              using `TU/FandolSong-Regular(0)/m/n' instead on input line 58.


LaTeX Warning: Citation `priyadarshan2017biology' on page 1 undefined on input line 59.


LaTeX Warning: Citation `ke2011review' on page 1 undefined on input line 61.


LaTeX Warning: Citation `gougeon1998automatic' on page 1 undefined on input line 61.


LaTeX Warning: Citation `culvenor2002tida' on page 1 undefined on input line 61.


LaTeX Warning: Citation `erikson2003segmentation' on page 1 undefined on input line 61.


LaTeX Warning: Citation `jing2012automated' on page 1 undefined on input line 63.


LaTeX Warning: Citation `yang2014automated' on page 1 undefined on input line 63.


LaTeX Warning: Citation `wang2004automated' on page 1 undefined on input line 63.


LaTeX Warning: Citation `wang2010crown' on page 1 undefined on input line 63.


LaTeX Warning: Citation `lamar2005automated' on page 1 undefined on input line 63.


LaTeX Warning: Citation `tong2021improved' on page 1 undefined on input line 63.


LaTeX Warning: Citation `zhao2023review' on page 1 undefined on input line 65.


LaTeX Warning: Citation `lassalle2022cnn' on page 1 undefined on input line 65.


LaTeX Warning: Citation `freudenberg2022individual' on page 1 undefined on input line 65.


LaTeX Warning: Citation `ronneberger2015u' on page 1 undefined on input line 65.


LaTeX Warning: Citation `he2017mask' on page 1 undefined on input line 65.


LaTeX Warning: Citation `braga2020amazon' on page 1 undefined on input line 65.


LaTeX Warning: Citation `hao2021individual' on page 1 undefined on input line 65.


LaTeX Warning: Citation `ball2023detectree2' on page 1 undefined on input line 65.

[1


]

LaTeX Warning: Citation `priyadarshan2017biology' on page 2 undefined on input line 84.


LaTeX Warning: Reference `fig:study_area' on page 2 undefined on input line 86.

File: fig/Study Area.pdf Graphic file (type pdf)
<use fig/Study Area.pdf>

LaTeX Warning: Reference `tab:datasets' on page 2 undefined on input line 96.

LaTeX Font Info:    Trying to load font information for U+msa on input line 104.
(C:\Program Files\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 104.
 (C:\Program Files\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

LaTeX Warning: Reference `tab:phenology' on page 2 undefined on input line 135.


Overfull \hbox (17.16685pt too wide) detected at line 166
[][][][][]
 []

[2]
Underfull \hbox (badness 2310) in paragraph at lines 172--173
\TU/FandolSong-Regular(0)/m/n/10 先 通 过 拉 普 拉 斯 变 换 和 残 差 块 提 取 特 征 金 字 塔
 []


Underfull \hbox (badness 1418) in paragraph at lines 172--173
\OMS/cmsy/m/n/10 f\OML/cmm/m/it/10 F[]; F[]; F[]; F[]; F[]\OMS/cmsy/m/n/10 g$\TU/FandolSong-Regular(0)/m/n/10 ；|  然 后 \TU/ptm/bx/it/10 GM-Mamba \TU/FandolSong-Regular(0)/m/n/10 模 块 对 每
 []


Underfull \vbox (badness 10000) has occurred while \output is active []

 [3] [4]
Package hyperref Info: bookmark level for unknown algorithm defaults to 0 on input line 390.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:393: Undefined control sequence.
l.393 \Require
              
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:396: Undefined control sequence.
l.396 \Ensure
             
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:399: Undefined control sequence.
l.399 \State
             初始化：MemoryBuffer $\mathcal{B} \gets \emptyset$（容量100），Q表 $Q \ge...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:400: Undefined control sequence.
l.400 \State
             设置阶段划分点：$t_1 = 0.3T$，$t_2 = 0.7T$，损失历史 $\mathcal{H} \gets \empt...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:401: Undefined control sequence.
l.401 \State
             初始化智能体：
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:402: Undefined control sequence.
l.402     \State
                \quad SA($T_0=100$, $\gamma=0.95$)
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:403: Undefined control sequence.
l.403     \State
                \quad QL($\alpha_{\mathrm{lr}}=0.1$, $\epsilon=0.1$)
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:404: Undefined control sequence.
l.404     \State
                \quad GA($N=20$, $G=10$)
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:407: You can't use `\prevdepth' in horizontal mode.
\nointerlineskip ->\prevdepth 
                              -\@m \p@ 
l.407     \State 计
                  算当前损失：$\mathcal{L}_{\mathrm{current}} \gets \text{Forward}...
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:407: Missing $ inserted.
<inserted text> 
                $
l.407     \State 计
                  算当前损失：$\mathcal{L}_{\mathrm{current}} \gets \text{Forward}...
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:407: Missing number, treated as zero.
<to be read again> 
                   \advance 
l.407     \State 计
                  算当前损失：$\mathcal{L}_{\mathrm{current}} \gets \text{Forward}...
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:407: Illegal unit of measure (pt inserted).
<to be read again> 
                   \advance 
l.407     \State 计
                  算当前损失：$\mathcal{L}_{\mathrm{current}} \gets \text{Forward}...
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:407: You can't use `\moveright' in math mode.
\algocf@push ...dvance \skiptotal by #1\moveright 
                                                  #1
l.407     \State 计
                  算当前损失：$\mathcal{L}_{\mathrm{current}} \gets \text{Forward}...
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:407: Missing number, treated as zero.
<to be read again> 
                   \hbox 
l.407     \State 计
                  算当前损失：$\mathcal{L}_{\mathrm{current}} \gets \text{Forward}...
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:407: Illegal unit of measure (pt inserted).
<to be read again> 
                   \hbox 
l.407     \State 计
                  算当前损失：$\mathcal{L}_{\mathrm{current}} \gets \text{Forward}...
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:407: Undefined control sequence.
<argument> \State 
                  
l.407     \State 计
                  算当前损失：$\mathcal{L}_{\mathrm{current}} \gets \text{Forward}...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:407: Missing $ inserted.
<inserted text> 
                $
l.407     \State 计
                  算当前损失：$\mathcal{L}_{\mathrm{current}} \gets \text{Forward}...
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

Missing character: There is no è in font cmex10!

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:407: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.407     \State 计
                  算当前损失：$\mathcal{L}_{\mathrm{current}} \gets \text{Forward}...
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:408: Undefined control sequence.
l.408     \State
                 从缓冲区采样：$\mathcal{B}_{\mathrm{sample}} \gets \text{Importanc...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:409: Undefined control sequence.
l.409     \State
                 计算回放损失：$\mathcal{L}_{\mathrm{replay}} \gets \text{Forward}(...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:410: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.410 
      
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:412: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.412         \Comment{
                       阶段1：模拟退火全局搜索}
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:412: Undefined control sequence.
<argument> \Comment 
                    
l.412         \Comment{
                       阶段1：模拟退火全局搜索}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:413: Undefined control sequence.
l.413         \State
                     生成候选解：$\alpha_{\mathrm{new}} \gets \alpha_{t-1} + \math...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:414: Undefined control sequence.
l.414         \State
                     计算能量差：$\Delta E \gets \mathcal{L}_{\mathrm{continual}}(...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:415: Undefined control sequence.
l.415         \State
                     计算接受概率：$P \gets \min\!\bigl(1,\,\exp(-\Delta E/T_k)\bigr)$
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:415: Missing { inserted.
<to be read again> 
                   \hbox 
l.415         \State 计算接受概率：$P \gets \min\!\bigl(
                                                 1,\,\exp(-\Delta E/T_k)\bigr)$
A left brace was mandatory here, so I've put one in.
You might want to delete and/or insert some corrections
so that I will find a matching right brace soon.
(If you're confused by all this, try typing `I}' now.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:415: Missing { inserted.
<to be read again> 
                   \hbox 
l.415 ...\min\!\bigl(1,\,\exp(-\Delta E/T_k)\bigr)
                                                  $
A left brace was mandatory here, so I've put one in.
You might want to delete and/or insert some corrections
so that I will find a matching right brace soon.
(If you're confused by all this, try typing `I}' now.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:415: Missing } inserted.
<inserted text> 
                }
l.415 ...min\!\bigl(1,\,\exp(-\Delta E/T_k)\bigr)$
                                                  
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:415: Missing } inserted.
<inserted text> 
                }
l.415 ...min\!\bigl(1,\,\exp(-\Delta E/T_k)\bigr)$
                                                  
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:417: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.417             \State $
                          \alpha_t \gets \alpha_{\mathrm{new}}$
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Underfull \hbox (badness 6094) in paragraph at lines 412--417
[]\TU/FandolSong-Regular(0)/m/n/10 阶 段 \TU/ptm/bx/it/10 1\TU/FandolSong-Regular(0)/m/n/10 ：|  模 拟 退 火 全 局 搜 索 生 成 候 选 解：|
 []

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:417: Undefined control sequence.
<argument> \State 
                  
l.417             \State $
                          \alpha_t \gets \alpha_{\mathrm{new}}$
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:419: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.419             \State $
                          \alpha_t \gets \alpha_{t-1}$
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:419: Undefined control sequence.
<argument> \State 
                  
l.419             \State $
                          \alpha_t \gets \alpha_{t-1}$
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:420: Undefined control sequence.
l.420         \EndIf
                    
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:421: Undefined control sequence.
l.421         \State
                     更新温度：$T_k \gets T_0 \cdot \gamma^{\,t}$   \Comment{或显式计...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:421: Undefined control sequence.
l.421 ... \gets T_0 \cdot \gamma^{\,t}$   \Comment
                                                  {或显式计数器 $k$}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:422: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.422 
      
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:423: Undefined control sequence.
l.423     \ElsIf
                {$t \le t_2$}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:424: Undefined control sequence.
l.424         \Comment
                      {阶段2：强化学习策略优化}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:425: Undefined control sequence.
l.425         \State
                     构建状态：$s_t \gets \lfloor 10\,\mathcal{L}_{\mathrm{curren...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:426: Undefined control sequence.
l.426         \State
                     选择动作：$a_t \gets \epsilon\text{-greedy}\bigl(Q(s_t,\!:\!...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:426: Missing { inserted.
<to be read again> 
                   \hbox 
l.426 ...作：$a_t \gets \epsilon\text{-greedy}\bigl(
                                                  Q(s_t,\!:\!)\bigr)$
A left brace was mandatory here, so I've put one in.
You might want to delete and/or insert some corrections
so that I will find a matching right brace soon.
(If you're confused by all this, try typing `I}' now.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:426: Missing { inserted.
<to be read again> 
                   \hbox 
l.426 ...lon\text{-greedy}\bigl(Q(s_t,\!:\!)\bigr)
                                                  $
A left brace was mandatory here, so I've put one in.
You might want to delete and/or insert some corrections
so that I will find a matching right brace soon.
(If you're confused by all this, try typing `I}' now.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:426: Missing } inserted.
<inserted text> 
                }
l.426 ...on\text{-greedy}\bigl(Q(s_t,\!:\!)\bigr)$
                                                  
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:426: Missing } inserted.
<inserted text> 
                }
l.426 ...on\text{-greedy}\bigl(Q(s_t,\!:\!)\bigr)$
                                                  
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:427: Undefined control sequence.
l.427         \State
                     执行动作：$\alpha_t \gets \text{clip}\!\bigl(\alpha_{t-1}+\D...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:427: Missing { inserted.
<to be read again> 
                   \hbox 
l.427 ... 执行动作：$\alpha_t \gets \text{clip}\!\bigl(
                                                  \alpha_{t-1}+\Delta\alpha(...
A left brace was mandatory here, so I've put one in.
You might want to delete and/or insert some corrections
so that I will find a matching right brace soon.
(If you're confused by all this, try typing `I}' now.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:427: Missing { inserted.
<to be read again> 
                   \hbox 
l.427 ...pha_{t-1}+\Delta\alpha(a_t),\,0,\,1\bigr)
                                                  $
A left brace was mandatory here, so I've put one in.
You might want to delete and/or insert some corrections
so that I will find a matching right brace soon.
(If you're confused by all this, try typing `I}' now.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:427: Missing } inserted.
<inserted text> 
                }
l.427 ...ha_{t-1}+\Delta\alpha(a_t),\,0,\,1\bigr)$
                                                  
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:427: Missing } inserted.
<inserted text> 
                }
l.427 ...ha_{t-1}+\Delta\alpha(a_t),\,0,\,1\bigr)$
                                                  
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:428: Undefined control sequence.
l.428         \State
                     计算奖励：$r_t \gets -\mathcal{L}_{\mathrm{continual}}(\alph...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:429: Undefined control sequence.
l.429         \State
                     更新Q值：
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:430: Undefined control sequence.
l.430             \State
                        \quad $Q(s_{\mathrm{prev}},a_{\mathrm{prev}}) \gets ...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:430: Missing { inserted.
<to be read again> 
                   \hbox 
l.430 ...hrm{prev}},a_{\mathrm{prev}}) + 0.1\bigl(
                                                  r_t + 0.9\max_{a'}Q(s_t,a'...
A left brace was mandatory here, so I've put one in.
You might want to delete and/or insert some corrections
so that I will find a matching right brace soon.
(If you're confused by all this, try typing `I}' now.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:430: Missing { inserted.
<to be read again> 
                   \hbox 
l.430 ....1\bigl(r_t + 0.9\max_{a'}Q(s_t,a')\bigr)
                                                  $
A left brace was mandatory here, so I've put one in.
You might want to delete and/or insert some corrections
so that I will find a matching right brace soon.
(If you're confused by all this, try typing `I}' now.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:430: Missing } inserted.
<inserted text> 
                }
l.430 ...1\bigl(r_t + 0.9\max_{a'}Q(s_t,a')\bigr)$
                                                  
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:430: Missing } inserted.
<inserted text> 
                }
l.430 ...1\bigl(r_t + 0.9\max_{a'}Q(s_t,a')\bigr)$
                                                  
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:431: Undefined control sequence.
l.431         \State
                     缓存前一步状态与动作：$s_{\mathrm{prev}} \gets s_t,\; a_{\mathrm{p...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:432: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.432 
      
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:434: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.434         \Comment{
                       阶段3：遗传算法局部精调}
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:434: Undefined control sequence.
<argument> \Comment 
                    
l.434         \Comment{
                       阶段3：遗传算法局部精调}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:435: Undefined control sequence.
l.435         \State
                     记录前两阶段最优解：$\alpha_{\mathrm{best}} \gets \arg\min_{\alph...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:436: Undefined control sequence.
l.436         \State
                     初始化种群：$P \gets \{\alpha_i\}_{i=1}^{20}$，其中 $\alpha_i \s...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:438: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.438             \State 评
                          估适应度：$f_i \gets -\mathcal{L}_{\mathrm{continual}}(...
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:438: Undefined control sequence.
<argument> \State 
                  
l.438             \State 评
                          估适应度：$f_i \gets -\mathcal{L}_{\mathrm{continual}}(...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:439: Undefined control sequence.
l.439             \State
                         锦标赛选择：$(p_1,p_2) \gets \text{TournamentSelect}(P,k=3)$
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:440: Undefined control sequence.
l.440             \State
                         算术交叉：$(c_1,c_2) \gets \text{ArithmeticCrossover}(p_...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:441: Undefined control sequence.
l.441             \State
                         高斯变异：$m_1 \gets c_1 + \mathcal{N}(0,0.02^2),\; m_2 ...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:442: Undefined control sequence.
l.442             \State
                         更新种群：$P \gets \text{Select}(P \cup \{c_1,c_2,m_1,m_...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:443: Undefined control sequence.
l.443         \EndFor
                     
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:444: Undefined control sequence.
l.444         \State
                     $\alpha_t \gets \arg\max_{\alpha\in P} f(\alpha)$
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:445: Undefined control sequence.
l.445     \EndIf
                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:446: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.446 
      
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:447: Undefined control sequence.
l.447     \State
                 联合训练：$\theta \gets \theta - \nabla_\theta\!\bigl(\alpha_t\m...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:447: Missing { inserted.
<to be read again> 
                   \hbox 
l.447 ...heta \gets \theta - \nabla_\theta\!\bigl(
                                                  \alpha_t\mathcal{L}_{\math...
A left brace was mandatory here, so I've put one in.
You might want to delete and/or insert some corrections
so that I will find a matching right brace soon.
(If you're confused by all this, try typing `I}' now.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:447: Missing { inserted.
<to be read again> 
                   \hbox 
l.447 ...pha_t)\mathcal{L}_{\mathrm{replay}}\bigr)
                                                  $
A left brace was mandatory here, so I've put one in.
You might want to delete and/or insert some corrections
so that I will find a matching right brace soon.
(If you're confused by all this, try typing `I}' now.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:447: Missing } inserted.
<inserted text> 
                }
l.447 ...ha_t)\mathcal{L}_{\mathrm{replay}}\bigr)$
                                                  
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:447: Missing } inserted.
<inserted text> 
                }
l.447 ...ha_t)\mathcal{L}_{\mathrm{replay}}\bigr)$
                                                  
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:448: Undefined control sequence.
l.448     \State
                 更新缓冲区：$\mathcal{B} \gets \mathcal{B} \cup \text{RandomSampl...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:449: Undefined control sequence.
l.449     \State
                 更新历史：$\mathcal{H} \gets \mathcal{H} \cup \{\mathcal{L}_{\ma...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:450: Undefined control sequence.
l.450 \EndFor
             
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:451: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.451 \end{algorithmic}
                       
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:452: Extra }, or forgotten \endgroup.
\@algocf@finish ...ideskip \egroup \hfill \egroup 
                                                  \ifthenelse {\boolean {alg...
l.452 \end{algorithm}
                     
I've deleted a group-closing symbol because it seems to be
spurious, as in `$x}$'. But perhaps the } is legitimate and
you forgot something else, as in `\hbox{$x}'. In such cases
the way to recover is to insert both the forgotten and the
deleted material, e.g., by typing `I$}'.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:452: Extra }, or forgotten \endgroup.
\@algocf@finish ... {\skipalgocfslide }}{}\egroup 
                                                  \end {lrbox}\algocf@maketh...
l.452 \end{algorithm}
                     
I've deleted a group-closing symbol because it seems to be
spurious, as in `$x}$'. But perhaps the } is legitimate and
you forgot something else, as in `\hbox{$x}'. In such cases
the way to recover is to insert both the forgotten and the
deleted material, e.g., by typing `I$}'.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:452: LaTeX Error: \begin{algorithm} on input line 389 ended by \end{lrbox}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.452 \end{algorithm}
                     
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:452: Missing } inserted.
<inserted text> 
                }
l.452 \end{algorithm}
                     
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:452: Too many }'s.
\@endfloatbox ...pagefalse \outer@nobreak \egroup 
                                                  \color@endbox 
l.452 \end{algorithm}
                     
You've closed more groups than you opened.
Such booboos are generally harmless, so keep going.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:452: Extra \endgroup.
\color@endgroup ->\endgraf \endgroup 
                                     
l.452 \end{algorithm}
                     
Things are pretty mixed up, but I think the worst is over.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:452: Too many }'s.
\color@endbox ->\color@endgroup \egroup 
                                        
l.452 \end{algorithm}
                     
You've closed more groups than you opened.
Such booboos are generally harmless, so keep going.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:452: LaTeX Error: \begin{document} ended by \end{algocf}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.452 \end{algorithm}
                     
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:452: Extra \endgroup.
<recently read> \endgroup 
                          
l.452 \end{algorithm}
                     
Things are pretty mixed up, but I think the worst is over.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:452: LaTeX Error: \begin{document} ended by \end{algocf@algorithm}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.452 \end{algorithm}
                     
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:452: Extra \endgroup.
<recently read> \endgroup 
                          
l.452 \end{algorithm}
                     
Things are pretty mixed up, but I think the worst is over.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:452: LaTeX Error: \begin{document} ended by \end{algorithm}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.452 \end{algorithm}
                     
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:452: Extra \endgroup.
<recently read> \endgroup 
                          
l.452 \end{algorithm}
                     
Things are pretty mixed up, but I think the worst is over.


Overfull \hbox (20.0pt too wide) in paragraph at lines 454--455
[]\TU/FandolSong-Regular(0)/m/n/10 经
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 验
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 回
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 放
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 机
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 制
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 采
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 用
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 时
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 相
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 感
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 知
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 重
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 要
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 性
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 采
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 样
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 策
 []


Overfull \hbox (13.08pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 略，|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 维
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 护
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 一
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 个
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 固
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 定
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 容
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 量
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (25.27pt too wide) in paragraph at lines 454--455
\TU/ptm/bx/it/10 Mem-
 []


Overfull \hbox (8.33pt too wide) in paragraph at lines 454--455
\TU/ptm/bx/it/10 o-
 []


Overfull \hbox (12.53pt too wide) in paragraph at lines 454--455
\TU/ptm/bx/it/10 ry-
 []


Overfull \hbox (26.83pt too wide) in paragraph at lines 454--455
\TU/ptm/bx/it/10 Buffer
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 来
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 存
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 储
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 历
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 史
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 时
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 相
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 特
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 征
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 表
 []


Overfull \hbox (13.56pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 示。|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 对
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 于
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 缓
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 冲
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 区
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 中
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 样
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 本
 []


Overfull \hbox (43.96368pt too wide) in paragraph at lines 454--455
\OT1/cmr/m/n/10 (\OML/cmm/m/it/10 x[]; y[]; t[]\OT1/cmr/m/n/10 )$\TU/FandolSong-Regular(0)/m/n/10 ，|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 其
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 采
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 样
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 概
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 率
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 定
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 义
 []


Overfull \hbox (13.03pt too wide) in paragraph at lines 454--455
\TU/FandolSong-Regular(0)/m/n/10 为：| 
 []

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:455: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.455 
      
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

[5]
Overfull \hbox (10.0pt too wide) in paragraph at lines 456--456
[] 
 []


Overfull \hbox (169.40022pt too wide) detected at line 461
[][][][][]
 []


Overfull \hbox (20.0pt too wide) in paragraph at lines 463--464
[]\TU/FandolSong-Regular(0)/m/n/10 其
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 中
 []


Overfull \hbox (32.49779pt too wide) in paragraph at lines 463--464
[]\OT1/cmr/m/n/10 (\OMS/cmsy/m/n/10 ^^A\OML/cmm/m/it/10 ; \OMS/cmsy/m/n/10 ^^A\OT1/cmr/m/n/10 )$
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 为
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 时
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 相
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 变
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 异
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 度
 []


Overfull \hbox (12.08372pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 量，|
 []


Overfull \hbox (21.47923pt too wide) in paragraph at lines 463--464
\OML/cmm/m/it/10 ^^Z[]\OT1/cmr/m/n/10 (\OML/cmm/m/it/10 t\OT1/cmr/m/n/10 )$
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 为
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 第
 []


Overfull \hbox (5.52084pt too wide) in paragraph at lines 463--464
\OML/cmm/m/it/10 k$
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 个
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 光
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 谱
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 波
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 段
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 在
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 时
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 相
 []


Overfull \hbox (3.61111pt too wide) in paragraph at lines 463--464
\OML/cmm/m/it/10 t$
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 平
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 均
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 反
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 射
 []


Overfull \hbox (12.08372pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 率，|
 []


Overfull \hbox (16.73953pt too wide) in paragraph at lines 463--464
\OML/cmm/m/it/10 ^^L \OT1/cmr/m/n/10 =
 []


Overfull \hbox (12.77782pt too wide) in paragraph at lines 463--464
\OT1/cmr/m/n/10 1\OML/cmm/m/it/10 :\OT1/cmr/m/n/10 0$
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 为
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 温
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 度
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 参
 []


Overfull \hbox (13.56pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 数。|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 该
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 采
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 样
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 策
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 略
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 确
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 保
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 与
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 当
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 前
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 时
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 相
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 相
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 似
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 历
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 史
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 样
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 本
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 有
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 更
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 高
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 被
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 选
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 中
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 概
 []


Overfull \hbox (13.08pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 率，|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 提
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 高
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 回
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 放
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 有
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 效
 []


Overfull \hbox (13.56pt too wide) in paragraph at lines 463--464
\TU/FandolSong-Regular(0)/m/n/10 性。| 
 []


Overfull \hbox (20.0pt too wide) in paragraph at lines 465--466
[]\TU/FandolSong-Regular(0)/m/n/10 模
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 块
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 与
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 主
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 网
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 络
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 集
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 成
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 通
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 过
 []


Overfull \hbox (31.94pt too wide) in paragraph at lines 465--466
\TU/ptm/bx/it/10 iCaRL-
 []


Overfull \hbox (15.83pt too wide) in paragraph at lines 465--466
\TU/ptm/bx/it/10 Net
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 架
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 构
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 实
 []


Overfull \hbox (13.56pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 现。|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 在
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 每
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 个
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 训
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 练
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 步
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 骤
 []


Overfull \hbox (13.08pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 中，|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 首
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 先
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 使
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 用
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 当
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 前
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 遗
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 忘
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 因
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 子
 []


Overfull \hbox (9.91786pt too wide) in paragraph at lines 465--466
\OML/cmm/m/it/10 ^^K[]$
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 计
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 算
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 联
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 合
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 损
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 失
 []


Overfull \hbox (36.407pt too wide) in paragraph at lines 465--466
\OMS/cmsy/m/n/10 L[] \OT1/cmr/m/n/10 =
 []


Overfull \hbox (39.80376pt too wide) in paragraph at lines 465--466
\OML/cmm/m/it/10 ^^K[]\OMS/cmsy/m/n/10 L[] \OT1/cmr/m/n/10 +
 []


Overfull \hbox (16.66672pt too wide) in paragraph at lines 465--466
\OT1/cmr/m/n/10 (1 \OMS/cmsy/m/n/10 ^^@
 []


Overfull \hbox (47.34258pt too wide) in paragraph at lines 465--466
\OML/cmm/m/it/10 ^^K[]\OT1/cmr/m/n/10 )\OMS/cmsy/m/n/10 L[]$\TU/FandolSong-Regular(0)/m/n/10 ，|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 然
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 后
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 通
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 过
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 标
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 准
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 反
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 向
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 传
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 播
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 更
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 新
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 网
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 络
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 参
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 数
 []


Overfull \hbox (8.53223pt too wide) in paragraph at lines 465--466
\OML/cmm/m/it/10 ^^R$\TU/FandolSong-Regular(0)/m/n/10 。|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 同
 []


Overfull \hbox (13.08pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 时，|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 三
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 个
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 智
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 能
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 体
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 根
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 据
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 当
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 前
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 损
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 失
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 变
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 化
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 和
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 特
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 征
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 统
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 计
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 量
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 协
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 作
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 调
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 整
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 遗
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 忘
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 因
 []


Overfull \hbox (13.08pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 子，|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 形
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 成
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 一
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 个
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 闭
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 环
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 自
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 适
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 应
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 优
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 化
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 系
 []


Overfull \hbox (13.56pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 统。|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 实
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 验
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 表
 []


Overfull \hbox (13.08pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 明，|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 该
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 机
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 制
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 能
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 够
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 在
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 橡
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 胶
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 树
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 极
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 端
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 物
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 候
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 变
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 化
 []


Overfull \hbox (29.63pt too wide) in paragraph at lines 465--466
|\TU/FandolSong-Regular(0)/m/n/10 （\TU/ptm/bx/it/10 NDVI
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 从
 []


Overfull \hbox (17.78pt too wide) in paragraph at lines 465--466
\TU/ptm/bx/it/10 0.85
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 到
 []


Overfull \hbox (21.44pt too wide) in paragraph at lines 465--466
\TU/ptm/bx/it/10 0.25\TU/FandolSong-Regular(0)/m/n/10 ）|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 情
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 况
 []


Overfull \hbox (13.08pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 下，|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 将
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 模
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 型
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 在
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 历
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 史
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 时
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 相
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 上
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 性
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 能
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 退
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 化
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 控
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 制
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 在
 []


Overfull \hbox (13.33pt too wide) in paragraph at lines 465--466
\TU/ptm/bx/it/10 5%
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 以
 []


Overfull \hbox (13.08pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 内，|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 显
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 著
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 优
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 于
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 传
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 统
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 序
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 贯
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 学
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 习
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 方
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 法
 []


Overfull \hbox (13.66pt too wide) in paragraph at lines 465--466
|\TU/FandolSong-Regular(0)/m/n/10 （性
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 能
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 退
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 化
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 通
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 常
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 超
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 过
 []


Overfull \hbox (25.55pt too wide) in paragraph at lines 465--466
\TU/ptm/bx/it/10 30%\TU/FandolSong-Regular(0)/m/n/10 ） 。|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 整
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 个
 []


Overfull \hbox (33.06pt too wide) in paragraph at lines 465--466
\TU/ptm/bx/it/10 MASA-
 []


Overfull \hbox (43.92pt too wide) in paragraph at lines 465--466
\TU/ptm/bx/it/10 Optimizer
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 机
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 制
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 通
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 过
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 多
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 智
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 能
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 体
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 协
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 作
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 自
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 适
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 应
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 调
 []


Overfull \hbox (13.08pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 节，|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 有
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 效
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 解
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 决
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 了
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 跨
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 物
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 候
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 期
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 训
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 练
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 中
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 灾
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 难
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 性
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 遗
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 忘
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 问
 []


Overfull \hbox (13.08pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 题，|
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 显
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 著
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 提
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 升
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 了
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 模
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 型
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 在
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 多
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 时
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 相
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 数
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 据
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 上
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 的
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 泛
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 化
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 能
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 力
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 和
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 鲁
 []


Overfull \hbox (10.0pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 棒
 []


Overfull \hbox (13.56pt too wide) in paragraph at lines 465--466
\TU/FandolSong-Regular(0)/m/n/10 性。| 
 []

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:466: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.466 
      
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

[6]
e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:466: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.466 
      
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

[7]
e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:466: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.466 
      
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

[8]
Overfull \hbox (15.3898pt too wide) in paragraph at lines 469--469
  \TU/lmr/m/sc/10 IV. 
 []


Overfull \hbox (17.33pt too wide) in paragraph at lines 469--469
 \TU/lmr/m/sc/10 Ex- 
 []


Overfull \hbox (20.96pt too wide) in paragraph at lines 469--469
 \TU/lmr/m/sc/10 per- 
 []


Overfull \hbox (6.8pt too wide) in paragraph at lines 469--469
 \TU/lmr/m/sc/10 i- 
 []


Overfull \hbox (22.96pt too wide) in paragraph at lines 469--469
 \TU/lmr/m/sc/10 men- 
 []


Overfull \hbox (16.5pt too wide) in paragraph at lines 469--469
 \TU/lmr/m/sc/10 tal 
 []


Overfull \hbox (29.31pt too wide) in paragraph at lines 469--469
 \TU/lmr/m/sc/10 Setup 
 []

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:469: Undefined control sequence.
\@afterheading ->\@nobreaktrue \everypar 
                                         {\if@nobreak \@nobreakfalse \clubpe...
l.469 \section{Experimental Setup}
                                  
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


Overfull \hbox (89.2pt too wide) in paragraph at lines 472--472
  []\TU/lmr/m/sc/10 Acknowledgment 
 []

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:472: Undefined control sequence.
\@afterheading ->\@nobreaktrue \everypar 
                                         {\if@nobreak \@nobreakfalse \clubpe...
l.472 \section*{Acknowledgment}
                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


Overfull \hbox (27.22pt too wide) in paragraph at lines 474--475
[]\TU/ptm/bx/it/10 The
 []


Overfull \hbox (13.89pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 au-
 []


Overfull \hbox (22.31pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 thors
 []


Overfull \hbox (25.84pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 would
 []


Overfull \hbox (15.0pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 like
 []


Overfull \hbox (8.89pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 to
 []


Overfull \hbox (25.29001pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 thank
 []


Overfull \hbox (13.89pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 the
 []


Overfull \hbox (29.45pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 anony-
 []


Overfull \hbox (22.83pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 mous
 []


Overfull \hbox (11.69pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 re-
 []


Overfull \hbox (23.05pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 view-
 []


Overfull \hbox (12.3pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 ers
 []


Overfull \hbox (11.98pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 for
 []


Overfull \hbox (20.59pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 their
 []


Overfull \hbox (21.39pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 valu-
 []


Overfull \hbox (17.78pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 able
 []


Overfull \hbox (21.1pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 com-
 []


Overfull \hbox (25.88pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 ments
 []


Overfull \hbox (16.12pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 and
 []


Overfull \hbox (17.83pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 sug-
 []


Overfull \hbox (16.71pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 ges-
 []


Overfull \hbox (23.95pt too wide) in paragraph at lines 474--475
\TU/ptm/bx/it/10 tions. 
 []

(ieee_tgrs_paper.bbl
Overfull \hbox (58.32pt too wide) in paragraph at lines 2--2
  []\TU/lmr/m/sc/10 References 
 []

ieee_tgrs_paper.bbl:2: Undefined control sequence.
\@afterheading ->\@nobreaktrue \everypar 
                                         {\if@nobreak \@nobreakfalse \clubpe...
l.2 \begin{thebibliography}{10}
                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:24: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.24 \bibitem{priyadarshan2017biology}
                                      
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:24: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.24 \bibitem{priyadarshan2017biology}
                                      
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:28: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.28 \bibitem{ke2011review}
                           
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:28: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.28 \bibitem{ke2011review}
                           
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:34: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.34 \bibitem{gougeon1998automatic}
                                   
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:34: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.34 \bibitem{gougeon1998automatic}
                                   
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:40: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.40 \bibitem{culvenor2002tida}
                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:40: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.40 \bibitem{culvenor2002tida}
                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:45: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.45 \bibitem{erikson2003segmentation}
                                      
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:45: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.45 \bibitem{erikson2003segmentation}
                                      
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:50: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.50 \bibitem{jing2012automated}
                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:50: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.50 \bibitem{jing2012automated}
                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:56: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.56 \bibitem{yang2014automated}
                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:56: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.56 \bibitem{yang2014automated}
                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:62: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.62 \bibitem{wang2004automated}
                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:62: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.62 \bibitem{wang2004automated}
                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:67: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.67 \bibitem{wang2010crown}
                            
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:67: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.67 \bibitem{wang2010crown}
                            
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:72: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.72 \bibitem{lamar2005automated}
                                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:72: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.72 \bibitem{lamar2005automated}
                                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:78: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.78 \bibitem{tong2021improved}
                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:78: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.78 \bibitem{tong2021improved}
                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:84: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.84 \bibitem{zhao2023review}
                             
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:84: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.84 \bibitem{zhao2023review}
                             
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:90: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.90 \bibitem{lassalle2022cnn}
                              
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:90: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.90 \bibitem{lassalle2022cnn}
                              
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:95: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.95 \bibitem{freudenberg2022individual}
                                        
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:95: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.95 \bibitem{freudenberg2022individual}
                                        
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:101: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.101 \bibitem{ronneberger2015u}
                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:101: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.101 \bibitem{ronneberger2015u}
                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:107: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.107 \bibitem{he2017mask}
                          
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:107: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.107 \bibitem{he2017mask}
                          
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:112: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.112 \bibitem{braga2020amazon}
                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:112: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.112 \bibitem{braga2020amazon}
                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:118: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.118 \bibitem{hao2021individual}
                                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:118: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.118 \bibitem{hao2021individual}
                                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:125: Undefined control sequence.
\@item ...\fi \global \@inlabeltrue \fi \everypar 
                                                  {\@minipagefalse \global \...
l.125 \bibitem{ball2023detectree2}
                                  
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:125: Undefined control sequence.
\@item ...se \clubpenalty \@clubpenalty \everypar 
                                                  {}\fi }\if@noitemarg \@noi...
l.125 \bibitem{ball2023detectree2}
                                  
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:132: Undefined control sequence.
\@doendpe ...rypar {}\par \@endpefalse }\everypar 
                                                  {{\setbox \z@ \lastbox }\e...
l.132 \end{thebibliography}
                           
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

ieee_tgrs_paper.bbl:132: Undefined control sequence.
\@doendpe ...ar {{\setbox \z@ \lastbox }\everypar 
                                                  {}\@endpefalse }
l.132 \end{thebibliography}
                           
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

)
e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:479: Undefined control sequence.
\par ...epar \clubpenalty \@clubpenalty \everypar 
                                                  {}\par \@endpefalse 
l.479 
      
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:480: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.480 \begin{IEEEbiography}{Firstname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

[9]
e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:480: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.480 \begin{IEEEbiography}{Firstname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:480: Undefined control sequence.
\@setminipage ->\@minipagetrue \everypar 
                                         {\@minipagefalse \everypar {}}
l.480 \begin{IEEEbiography}{Firstname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:480: Undefined control sequence.
\@setminipage ...rypar {\@minipagefalse \everypar 
                                                  {}}
l.480 \begin{IEEEbiography}{Firstname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:480: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.480 \begin{IEEEbiography}{Firstname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:480: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.480 \begin{IEEEbiography}{Firstname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:480: Undefined control sequence.
\@setminipage ->\@minipagetrue \everypar 
                                         {\@minipagefalse \everypar {}}
l.480 \begin{IEEEbiography}{Firstname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:480: Undefined control sequence.
\@setminipage ...rypar {\@minipagefalse \everypar 
                                                  {}}
l.480 \begin{IEEEbiography}{Firstname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:480: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.480 \begin{IEEEbiography}{Firstname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


Overfull \hbox (125.85175pt too wide) in paragraph at lines 480--482
[]\TU/lmr/bx/n/8 Firstname
 []


Overfull \hbox (103.97174pt too wide) in paragraph at lines 480--482
\TU/lmr/bx/n/8 Last-
 []


Overfull \hbox (105.25954pt too wide) in paragraph at lines 480--482
\TU/lmr/bx/n/8 name
 []


Overfull \hbox (92.30774pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 re-
 []


Overfull \hbox (105.04375pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 ceived
 []


Overfull \hbox (94.18774pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 the
 []


Overfull \hbox (97.84373pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 B.S.
 []


Overfull \hbox (93.71574pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 de-
 []


Overfull \hbox (97.49974pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 gree
 []


Overfull \hbox (89.46774pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 in
 []


Overfull \hbox (92.30774pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 re-
 []


Overfull \hbox (18.408pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 mote
 []


Overfull \hbox (18.032pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 sens-
 []


Overfull \hbox (11.328pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 ing
 []


Overfull \hbox (17.24pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 from
 []


Overfull \hbox (16.28pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 Uni-
 []


Overfull \hbox (14.168pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 ver-
 []


Overfull \hbox (13.264pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 sity
 []


Overfull \hbox (6.368pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 A
 []


Overfull \hbox (7.08pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 in
 []


Overfull \hbox (19.352pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 2015,
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 and
 []


Overfull \hbox (11.8pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 the
 []


Overfull \hbox (21.712pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 Ph.D.
 []


Overfull \hbox (11.328pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 de-
 []


Overfull \hbox (15.112pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 gree
 []


Overfull \hbox (7.08pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 in
 []


Overfull \hbox (15.104pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 geo-
 []


Overfull \hbox (25.536pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 science
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 and
 []


Overfull \hbox (9.92pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 re-
 []


Overfull \hbox (18.408pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 mote
 []


Overfull \hbox (18.032pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 sens-
 []


Overfull \hbox (11.328pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 ing
 []


Overfull \hbox (17.24pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 from
 []


Overfull \hbox (16.28pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 Uni-
 []


Overfull \hbox (14.168pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 ver-
 []


Overfull \hbox (13.264pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 sity
 []


Overfull \hbox (6.016pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 B
 []


Overfull \hbox (7.08pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 in
 []


Overfull \hbox (19.352pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 2020.
 []


Overfull \hbox (10.144pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 He
 []


Overfull \hbox (5.712pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 is
 []


Overfull \hbox (14.64pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 cur-
 []


Overfull \hbox (21.72pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 rently
 []


Overfull \hbox (4.248pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 a
 []


Overfull \hbox (12.864pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 Re-
 []


Overfull \hbox (22.944pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 search
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 Sci-
 []


Overfull \hbox (11.328pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 en-
 []


Overfull \hbox (12.32pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 tist
 []


Overfull \hbox (16.52pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 with
 []


Overfull \hbox (11.8pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 the
 []


Overfull \hbox (10.616pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 In-
 []


Overfull \hbox (11.848pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 sti-
 []


Overfull \hbox (15.104pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 tute
 []


Overfull \hbox (6.848pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 of
 []


Overfull \hbox (12.864pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 Re-
 []


Overfull \hbox (18.408pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 mote
 []


Overfull \hbox (19.4pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 Sens-
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 ing.
 []


Overfull \hbox (12.08pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 His
 []


Overfull \hbox (9.92pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 re-
 []


Overfull \hbox (22.944pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 search
 []


Overfull \hbox (9.912pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 in-
 []


Overfull \hbox (13.224pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 ter-
 []


Overfull \hbox (13.784pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 ests
 []


Overfull \hbox (9.912pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 in-
 []


Overfull \hbox (19.352pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 clude
 []


Overfull \hbox (16.992pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 deep
 []


Overfull \hbox (21.248pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 learn-
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 ing,
 []


Overfull \hbox (19.112pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 plant
 []


Overfull \hbox (16.048pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 phe-
 []


Overfull \hbox (11.8pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 no-
 []


Overfull \hbox (15.104pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 typ-
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 ing,
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 and
 []


Overfull \hbox (14.64pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 pre-
 []


Overfull \hbox (8.968pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 ci-
 []


Overfull \hbox (14.68001pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 sion
 []


Overfull \hbox (17.0pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 agri-
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 cul-
 []


Overfull \hbox (17.472pt too wide) in paragraph at lines 480--482
\TU/lmr/m/n/8 ture. 
 []

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:484: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.484 \begin{IEEEbiography}{Secondname Lastname}
                                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:484: Undefined control sequence.
\@setminipage ->\@minipagetrue \everypar 
                                         {\@minipagefalse \everypar {}}
l.484 \begin{IEEEbiography}{Secondname Lastname}
                                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:484: Undefined control sequence.
\@setminipage ...rypar {\@minipagefalse \everypar 
                                                  {}}
l.484 \begin{IEEEbiography}{Secondname Lastname}
                                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:484: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.484 \begin{IEEEbiography}{Secondname Lastname}
                                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:484: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.484 \begin{IEEEbiography}{Secondname Lastname}
                                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:484: Undefined control sequence.
\@setminipage ->\@minipagetrue \everypar 
                                         {\@minipagefalse \everypar {}}
l.484 \begin{IEEEbiography}{Secondname Lastname}
                                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:484: Undefined control sequence.
\@setminipage ...rypar {\@minipagefalse \everypar 
                                                  {}}
l.484 \begin{IEEEbiography}{Secondname Lastname}
                                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:484: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.484 \begin{IEEEbiography}{Secondname Lastname}
                                                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


Overfull \hbox (135.34775pt too wide) in paragraph at lines 484--486
[]\TU/lmr/bx/n/8 Secondname
 []


Overfull \hbox (103.97174pt too wide) in paragraph at lines 484--486
\TU/lmr/bx/n/8 Last-
 []


Overfull \hbox (105.25954pt too wide) in paragraph at lines 484--486
\TU/lmr/bx/n/8 name
 []


Overfull \hbox (92.30774pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 re-
 []


Overfull \hbox (105.04375pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 ceived
 []


Overfull \hbox (94.18774pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 the
 []


Overfull \hbox (104.09975pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 Ph.D.
 []


Overfull \hbox (93.71574pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 de-
 []


Overfull \hbox (97.49974pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 gree
 []


Overfull \hbox (89.46774pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 in
 []


Overfull \hbox (100.32375pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 com-
 []


Overfull \hbox (19.832pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 puter
 []


Overfull \hbox (12.32pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 sci-
 []


Overfull \hbox (16.048pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 ence
 []


Overfull \hbox (17.24pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 from
 []


Overfull \hbox (16.28pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 Uni-
 []


Overfull \hbox (14.168pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 ver-
 []


Overfull \hbox (13.264pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 sity
 []


Overfull \hbox (6.136pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 C
 []


Overfull \hbox (7.08pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 in
 []


Overfull \hbox (19.352pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 2018.
 []


Overfull \hbox (13.216pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 She
 []


Overfull \hbox (5.712pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 is
 []


Overfull \hbox (14.64pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 cur-
 []


Overfull \hbox (21.72pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 rently
 []


Overfull \hbox (8.968pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 an
 []


Overfull \hbox (12.552pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 As-
 []


Overfull \hbox (10.432pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 so-
 []


Overfull \hbox (17.464pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 ciate
 []


Overfull \hbox (16.176pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 Pro-
 []


Overfull \hbox (12.56pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 fes-
 []


Overfull \hbox (10.912pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 sor
 []


Overfull \hbox (16.52pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 with
 []


Overfull \hbox (11.8pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 the
 []


Overfull \hbox (13.096pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 De-
 []


Overfull \hbox (18.416pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 part-
 []


Overfull \hbox (18.64pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 ment
 []


Overfull \hbox (6.848pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 of
 []


Overfull \hbox (12.864pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 Re-
 []


Overfull \hbox (18.408pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 mote
 []


Overfull \hbox (19.4pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 Sens-
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 ing.
 []


Overfull \hbox (13.456pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 Her
 []


Overfull \hbox (9.92pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 re-
 []


Overfull \hbox (22.944pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 search
 []


Overfull \hbox (9.68001pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 fo-
 []


Overfull \hbox (18.976pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 cuses
 []


Overfull \hbox (8.968pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 on
 []


Overfull \hbox (14.16pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 ma-
 []


Overfull \hbox (19.112pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 chine
 []


Overfull \hbox (21.248pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 learn-
 []


Overfull \hbox (11.328pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 ing
 []


Overfull \hbox (11.8pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 ap-
 []


Overfull \hbox (12.272pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 pli-
 []


Overfull \hbox (10.856pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 ca-
 []


Overfull \hbox (17.984pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 tions
 []


Overfull \hbox (7.08pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 in
 []


Overfull \hbox (17.0pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 agri-
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 cul-
 []


Overfull \hbox (15.112pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 ture
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 and
 []


Overfull \hbox (11.328pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 en-
 []


Overfull \hbox (9.68001pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 vi-
 []


Overfull \hbox (15.112pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 ron-
 []


Overfull \hbox (18.408pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 men-
 []


Overfull \hbox (9.912pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 tal
 []


Overfull \hbox (18.88pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 mon-
 []


Overfull \hbox (5.192pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 i-
 []


Overfull \hbox (13.696pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 tor-
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 484--486
\TU/lmr/m/n/8 ing. 
 []

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:486: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.486 \end{IEEEbiography}
                         
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

[10]
e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:488: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.488 \begin{IEEEbiography}{Thirdname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:488: Undefined control sequence.
\@setminipage ->\@minipagetrue \everypar 
                                         {\@minipagefalse \everypar {}}
l.488 \begin{IEEEbiography}{Thirdname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:488: Undefined control sequence.
\@setminipage ...rypar {\@minipagefalse \everypar 
                                                  {}}
l.488 \begin{IEEEbiography}{Thirdname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:488: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.488 \begin{IEEEbiography}{Thirdname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:488: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.488 \begin{IEEEbiography}{Thirdname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:488: Undefined control sequence.
\@setminipage ->\@minipagetrue \everypar 
                                         {\@minipagefalse \everypar {}}
l.488 \begin{IEEEbiography}{Thirdname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:488: Undefined control sequence.
\@setminipage ...rypar {\@minipagefalse \everypar 
                                                  {}}
l.488 \begin{IEEEbiography}{Thirdname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:488: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.488 \begin{IEEEbiography}{Thirdname Lastname}
                                               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


Overfull \hbox (129.73975pt too wide) in paragraph at lines 488--490
[]\TU/lmr/bx/n/8 Thirdname
 []


Overfull \hbox (103.97174pt too wide) in paragraph at lines 488--490
\TU/lmr/bx/n/8 Last-
 []


Overfull \hbox (105.25954pt too wide) in paragraph at lines 488--490
\TU/lmr/bx/n/8 name
 []


Overfull \hbox (88.09975pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 is
 []


Overfull \hbox (86.63574pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 a
 []


Overfull \hbox (98.56374pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 Pro-
 []


Overfull \hbox (94.94774pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 fes-
 []


Overfull \hbox (93.29974pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 sor
 []


Overfull \hbox (96.07574pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 and
 []


Overfull \hbox (94.06775pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 Di-
 []


Overfull \hbox (96.08374pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 rec-
 []


Overfull \hbox (10.864pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 tor
 []


Overfull \hbox (6.848pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 of
 []


Overfull \hbox (11.8pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 the
 []


Overfull \hbox (12.864pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 Re-
 []


Overfull \hbox (18.408pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 mote
 []


Overfull \hbox (19.4pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 Sens-
 []


Overfull \hbox (11.328pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 ing
 []


Overfull \hbox (17.112pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 Lab-
 []


Overfull \hbox (7.08pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 o-
 []


Overfull \hbox (10.392pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 ra-
 []


Overfull \hbox (17.0pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 tory.
 []


Overfull \hbox (12.08pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 His
 []


Overfull \hbox (9.92pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 re-
 []


Overfull \hbox (22.944pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 search
 []


Overfull \hbox (9.912pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 in-
 []


Overfull \hbox (13.224pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 ter-
 []


Overfull \hbox (13.784pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 ests
 []


Overfull \hbox (9.912pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 in-
 []


Overfull \hbox (19.352pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 clude
 []


Overfull \hbox (11.8pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 hy-
 []


Overfull \hbox (14.88pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 per-
 []


Overfull \hbox (18.696pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 spec-
 []


Overfull \hbox (13.224pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 tral
 []


Overfull \hbox (9.92pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 re-
 []


Overfull \hbox (18.408pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 mote
 []


Overfull \hbox (18.032pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 sens-
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 ing,
 []


Overfull \hbox (16.056pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 crop
 []


Overfull \hbox (18.88pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 mon-
 []


Overfull \hbox (5.192pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 i-
 []


Overfull \hbox (13.696pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 tor-
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 ing,
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 and
 []


Overfull \hbox (14.64pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 pre-
 []


Overfull \hbox (8.968pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 ci-
 []


Overfull \hbox (14.68001pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 sion
 []


Overfull \hbox (17.0pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 agri-
 []


Overfull \hbox (13.688pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 cul-
 []


Overfull \hbox (15.112pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 ture
 []


Overfull \hbox (18.168pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 tech-
 []


Overfull \hbox (18.408pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 nolo-
 []


Overfull \hbox (16.09601pt too wide) in paragraph at lines 488--490
\TU/lmr/m/n/8 gies. 
 []

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:492: Undefined control sequence.
\@arrayparboxrestore ...parskip \z@skip \everypar 
                                                  {}\linewidth \hsize \@tota...
l.492 \end{document}
                    
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

[11] (ieee_tgrs_paper.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2022/08/05>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.


LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 16887 strings out of 409617
 349771 string characters out of 5778277
 2003191 words of memory out of 5000000
 38725 multiletter control sequences out of 15000+600000
 564258 words of font info for 118 fonts, out of 8000000 for 9000
 1351 hyphenation exceptions out of 8191
 79i,14n,93p,601b,458s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on ieee_tgrs_paper.xdv (11 pages, 241944 bytes).
