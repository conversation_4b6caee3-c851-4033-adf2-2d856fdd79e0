This is XeTeX, Version 3.141592653-2.6-0.999995 (MiKTeX 24.1) (preloaded format=xelatex 2025.8.1)  3 AUG 2025 21:17
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex
(e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(C:\Program Files\MiKTeX\tex/latex/ieeetran\IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count183
\@IEEEtrantmpcountB=\count184
\@IEEEtrantmpcountC=\count185
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 503.
LaTeX Font Info:    No file TUptm.fd. on input line 503.

LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 503.

-- Using 8.5in x 11in (letter) paper.
-- Using DVI output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146

LaTeX Font Warning: Font shape `TU/ptm/bx/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/bx/it' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 1090.

\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@IEEEsubequation=\count190
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count191
\c@table=\count192
\@IEEEeqnnumcols=\count193
\@IEEEeqncolcnt=\count194
\@IEEEsubeqnnumrollback=\count195
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count196
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count197
\@IEEEtranrubishbin=\box52
) (C:\Program Files\MiKTeX\tex/latex/cite\cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (C:\Program Files\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (C:\Program Files\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks18
) (C:\Program Files\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (C:\Program Files\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (C:\Program Files\MiKTeX\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
 (C:\Program Files\MiKTeX\tex/latex/graphics-def\xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen164
\Gin@req@width=\dimen165
) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(C:\Program Files\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (C:\Program Files\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen166
)) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen167
) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count198
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count199
\leftroot@=\count266
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count267
\DOTSCASE@=\count268
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen168
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count269
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count270
\dotsspace@=\muskip16
\c@parentequation=\count271
\dspbrk@lvl=\count272
\tag@help=\toks20
\row@=\count273
\column@=\count274
\maxfields@=\count275
\andhelp@=\toks21
\eqnshift@=\dimen169
\alignsep@=\dimen170
\tagshift@=\dimen171
\tagwidth@=\dimen172
\totwidth@=\dimen173
\lineht@=\dimen174
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (C:\Program Files\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (C:\Program Files\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (C:\Program Files\MiKTeX\tex/latex/algorithms\algorithm.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (C:\Program Files\MiKTeX\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count276
\float@exts=\toks24
\float@box=\box55
\@float@everytoks=\toks25
\@floatcapt=\box56
) (C:\Program Files\MiKTeX\tex/latex/base\ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks26
\c@algorithm=\count277
) (C:\Program Files\MiKTeX\tex/latex/algorithmicx\algpseudocode.sty
Package: algpseudocode 
 (C:\Program Files\MiKTeX\tex/latex/algorithmicx\algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count278
\c@ALG@rem=\count279
\c@ALG@nested=\count280
\ALG@tlm=\skip54
\ALG@thistlm=\skip55
\c@ALG@Lnr=\count281
\c@ALG@blocknr=\count282
\c@ALG@storecount=\count283
\c@ALG@tmpcounter=\count284
\ALG@tmplength=\skip56
)
Document Style - pseudocode environments for use with the `algorithmicx' style
) (C:\Program Files\MiKTeX\tex/latex/tools\array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen175
\ar@mcellbox=\box57
\extrarowheight=\dimen176
\NC@list=\toks27
\extratabsurround=\skip57
\backup@length=\skip58
\ar@cellbox=\box58
) (C:\Program Files\MiKTeX\tex/latex/mdwtools\mdwmath.sty
Package: mdwmath 1996/04/11 1.1 Nice mathematical things
\sq@sqrt=\count285
LaTeX Info: Redefining \sqrt on input line 84.
) (C:\Program Files\MiKTeX\tex/latex/mdwtools\mdwtab.sty
Package: mdwtab 1998/04/28 1.9 Table typesetting with style
\tab@state=\count286
\tab@columns=\count287
\tab@preamble=\toks28
\tab@shortline=\toks29
\extrarowheight=\dimen177
\tabextrasep=\dimen178
\arrayextrasep=\dimen179
\smarraycolsep=\dimen180
\smarrayextrasep=\dimen181
\tab@width=\dimen182
\col@sep=\dimen183
\tab@endheight=\dimen184
\tab@leftskip=\skip59
\tab@rightskip=\skip60
\fn@notes=\box59
\fn@width=\dimen185
) (C:\Program Files\MiKTeX\tex/latex/eqparbox\eqparbox.sty
Package: eqparbox 2017/09/03 v4.1 Create equal-widthed boxes
\eqp@tempdima=\skip61
\eqp@tempdimb=\skip62
\eqp@tabular@box=\box60
\eqp@list@box=\box61
\eqp@list@indent=\skip63
 (C:\Program Files\MiKTeX\tex/latex/environ\environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments
 (C:\Program Files\MiKTeX\tex/latex/trimspaces\trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
))) (C:\Program Files\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (C:\Program Files\MiKTeX\tex/latex/subfigure\subfigure.sty
Package: subfigure 2002/07/30 v2.1.4 subfigure package
\subfigtopskip=\skip64
\subfigcapskip=\skip65
\subfigcaptopadj=\dimen186
\subfigbottomskip=\skip66
\subfigcapmargin=\dimen187
\subfiglabelskip=\skip67
\c@subfigure=\count288
\c@lofdepth=\count289
\c@subtable=\count290
\c@lotdepth=\count291

****************************************
* Local config file subfigure.cfg used *
****************************************
(C:\Program Files\MiKTeX\tex/latex/subfigure\subfigure.cfg)
\subfig@top=\skip68
\subfig@bottom=\skip69
) (C:\Program Files\MiKTeX\tex/latex/multirow\multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip70
\multirow@cntb=\count292
\multirow@dima=\skip71
\bigstrutjot=\dimen188
) (C:\Program Files\MiKTeX\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen189
\lightrulewidth=\dimen190
\cmidrulewidth=\dimen191
\belowrulesep=\dimen192
\belowbottomsep=\dimen193
\aboverulesep=\dimen194
\abovetopsep=\dimen195
\cmidrulesep=\dimen196
\cmidrulekern=\dimen197
\defaultaddspace=\dimen198
\@cmidla=\count293
\@cmidlb=\count294
\@aboverulesep=\dimen199
\@belowrulesep=\dimen256
\@thisruleclass=\count295
\@lastruleclass=\count296
\@thisrulewidth=\dimen257
) (C:\Program Files\MiKTeX\tex/latex/graphics\color.sty
Package: color 2022/01/06 v1.3d Standard LaTeX Color (DPC)
 (C:\Program Files\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: xetex.def on input line 149.
 (C:\Program Files\MiKTeX\tex/latex/graphics\mathcolor.ltx)) (C:\Program Files\MiKTeX\tex/latex/preprint\balance.sty
Package: balance 1999/02/23 4.3 (PWD)
\oldvsize=\dimen258
) (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec.sty (C:\Program Files\MiKTeX\tex/latex/l3packages/xparse\xparse.sty (C:\Program Files\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2024-01-04 L3 programming layer (loader) 
 (C:\Program Files\MiKTeX\tex/latex/l3backend\l3backend-xetex.def
File: l3backend-xetex.def 2024-01-04 L3 backend support: XeTeX
\g__graphics_track_int=\count297
\l__pdf_internal_box=\box62
\g__pdf_backend_object_int=\count298
\g__pdf_backend_annotation_int=\count299
\g__pdf_backend_link_int=\count300
))
Package: xparse 2023-10-10 L3 Experimental document command parser
)
Package: fontspec 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
 (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec-xetex.sty
Package: fontspec-xetex 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count301
\l__fontspec_language_int=\count302
\l__fontspec_strnum_int=\count303
\l__fontspec_tmp_int=\count304
\l__fontspec_tmpa_int=\count305
\l__fontspec_tmpb_int=\count306
\l__fontspec_tmpc_int=\count307
\l__fontspec_em_int=\count308
\l__fontspec_emdef_int=\count309
\l__fontspec_strong_int=\count310
\l__fontspec_strongdef_int=\count311
\l__fontspec_tmpa_dim=\dimen259
\l__fontspec_tmpb_dim=\dimen260
\l__fontspec_tmpc_dim=\dimen261
 (C:\Program Files\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec.cfg))) (C:\Program Files\MiKTeX\tex/xelatex/xecjk\xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX
 (C:\Program Files\MiKTeX\tex/latex/ctex\ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (C:\Program Files\MiKTeX\tex/latex/l3packages/xtemplate\xtemplate.sty
Package: xtemplate 2023-10-10 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen262
\l__xtemplate_tmp_int=\count312
\l__xtemplate_tmp_muskip=\muskip18
\l__xtemplate_tmp_skip=\skip72
)
\l__xeCJK_tmp_int=\count313
\l__xeCJK_tmp_box=\box63
\l__xeCJK_tmp_dim=\dimen263
\l__xeCJK_tmp_skip=\skip73
\g__xeCJK_space_factor_int=\count314
\l__xeCJK_begin_int=\count315
\l__xeCJK_end_int=\count316
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip74
\c__xeCJK_none_node=\count317
\g__xeCJK_node_int=\count318
\c__xeCJK_CJK_node_dim=\dimen264
\c__xeCJK_CJK-space_node_dim=\dimen265
\c__xeCJK_default_node_dim=\dimen266
\c__xeCJK_CJK-widow_node_dim=\dimen267
\c__xeCJK_normalspace_node_dim=\dimen268
\c__xeCJK_default-space_node_skip=\skip75
\l__xeCJK_ccglue_skip=\skip76
\l__xeCJK_ecglue_skip=\skip77
\l__xeCJK_punct_kern_skip=\skip78
\l__xeCJK_indent_box=\box64
\l__xeCJK_last_penalty_int=\count319
\l__xeCJK_last_bound_dim=\dimen269
\l__xeCJK_last_kern_dim=\dimen270
\l__xeCJK_widow_penalty_int=\count320

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen271
\l__xeCJK_mixed_punct_width_dim=\dimen272
\l__xeCJK_middle_punct_width_dim=\dimen273
\l__xeCJK_fixed_margin_width_dim=\dimen274
\l__xeCJK_mixed_margin_width_dim=\dimen275
\l__xeCJK_middle_margin_width_dim=\dimen276
\l__xeCJK_bound_punct_width_dim=\dimen277
\l__xeCJK_bound_margin_width_dim=\dimen278
\l__xeCJK_margin_minimum_dim=\dimen279
\l__xeCJK_kerning_total_width_dim=\dimen280
\l__xeCJK_same_align_margin_dim=\dimen281
\l__xeCJK_different_align_margin_dim=\dimen282
\l__xeCJK_kerning_margin_width_dim=\dimen283
\l__xeCJK_kerning_margin_minimum_dim=\dimen284
\l__xeCJK_bound_dim=\dimen285
\l__xeCJK_reverse_bound_dim=\dimen286
\l__xeCJK_margin_dim=\dimen287
\l__xeCJK_minimum_bound_dim=\dimen288
\l__xeCJK_kerning_margin_dim=\dimen289
\g__xeCJK_family_int=\count321
\l__xeCJK_fam_int=\count322
\g__xeCJK_fam_allocation_int=\count323
\l__xeCJK_verb_case_int=\count324
\l__xeCJK_verb_exspace_skip=\skip79
 (C:\Program Files\MiKTeX\tex/xelatex/xecjk\xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
)) (C:\Program Files\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2023-11-26 v7.01g Hypertext links for LaTeX
 (C:\Program Files\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
) (C:\Program Files\MiKTeX\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (C:\Program Files\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (C:\Program Files\MiKTeX\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (C:\Program Files\MiKTeX\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (C:\Program Files\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (C:\Program Files\MiKTeX\tex/generic/pdftexcmds\pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (C:\Program Files\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (C:\Program Files\MiKTeX\tex/latex/letltxmacro\letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
) (C:\Program Files\MiKTeX\tex/latex/auxhook\auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (C:\Program Files\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (C:\Program Files\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (C:\Program Files\MiKTeX\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (C:\Program Files\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count325
) (C:\Program Files\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count326
)
\@linkdim=\dimen290
\Hy@linkcounter=\count327
\Hy@pagecounter=\count328
 (C:\Program Files\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2023-11-26 v7.01g Hyperref: PDFDocEncoding definition (HO)
) (C:\Program Files\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count329
 (C:\Program Files\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2023-11-26 v7.01g Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4064.
Package hyperref Info: Option `bookmarks' set `false' on input line 4064.
Package hyperref Info: Hyper figures OFF on input line 4181.
Package hyperref Info: Link nesting OFF on input line 4186.
Package hyperref Info: Hyper index ON on input line 4189.
Package hyperref Info: Plain pages OFF on input line 4196.
Package hyperref Info: Backreferencing OFF on input line 4201.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks OFF on input line 4454.
\c@Hy@tempcnt=\count330
LaTeX Info: Redefining \url on input line 4786.
\XeTeXLinkMargin=\dimen291
 (C:\Program Files\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (C:\Program Files\MiKTeX\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count331
\Field@Width=\dimen292
\Fld@charsize=\dimen293
Package hyperref Info: Hyper figures OFF on input line 6065.
Package hyperref Info: Link nesting OFF on input line 6070.
Package hyperref Info: Hyper index ON on input line 6073.
Package hyperref Info: backreferencing OFF on input line 6080.
Package hyperref Info: Link coloring ON on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6090.
Package hyperref Info: PDF/A mode OFF on input line 6095.
 (C:\Program Files\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count332
\c@Item=\count333
\c@Hfootnote=\count334
)
Package hyperref Info: Driver: hxetex.
 (C:\Program Files\MiKTeX\tex/latex/hyperref\hxetex.def
File: hxetex.def 2023-11-26 v7.01g Hyperref driver for XeTeX
 (C:\Program Files\MiKTeX\tex/generic/stringenc\stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box65
\c@Hy@AnnotLevel=\count335
\HyField@AnnotCount=\count336
\Fld@listcount=\count337
\c@bookmark@seq@number=\count338
 (C:\Program Files\MiKTeX\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)
 (C:\Program Files\MiKTeX\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 284.
)
\Hy@SectionHShift=\skip80
)

Package xeCJK Warning: Fandol is being set as the default font for CJK text.
(xeCJK)                Please make sure it has been properly installed.


Package fontspec Warning: Font "FandolSong-Regular" does not contain requested
(fontspec)                Script "CJK".


Package fontspec Info: Font family 'FandolSong-Regular(0)' created for font
(fontspec)             'FandolSong-Regular' with options
(fontspec)             [Script={CJK},Extension={.otf},BoldFont={FandolSong-Bold},ItalicFont={FandolKai-Regular}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[FandolSong-Regular.otf]/OT:language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"[FandolSong-Bold.otf]/OT:language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"[FandolKai-Regular.otf]/OT:language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 

(ieee_tgrs_paper.aux)
\openout1 = `ieee_tgrs_paper.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 31.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 31.
 (C:\Program Files\MiKTeX\tex/latex/base\ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.

-- Lines per column: 58 (exact).

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 31.
LaTeX Font Info:    Redeclaring math accent \acute on input line 31.
LaTeX Font Info:    Redeclaring math accent \grave on input line 31.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 31.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 31.
LaTeX Font Info:    Redeclaring math accent \bar on input line 31.
LaTeX Font Info:    Redeclaring math accent \breve on input line 31.
LaTeX Font Info:    Redeclaring math accent \check on input line 31.
LaTeX Font Info:    Redeclaring math accent \hat on input line 31.
LaTeX Font Info:    Redeclaring math accent \dot on input line 31.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 31.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 31.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 31.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 31.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 31.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 31.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 31.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 31.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 31.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 31.
Package hyperref Info: Link coloring ON on input line 31.

Underfull \hbox (badness 3679) in paragraph at lines 47--47
\TU/lmr/m/n/8 versity of Agriculture, City, State 12345 USA (e-mail: first-
 []


Underfull \hbox (badness 7613) in paragraph at lines 47--47
[][][]\TU/lmr/m/n/8 T. Lastname is with the Department of Computer Sci-
 []


Underfull \hbox (badness 3884) in paragraph at lines 47--47
\TU/lmr/m/n/8 ence, Technology University, City, State 12345 USA (e-mail:
 []


LaTeX Font Warning: Font shape `TU/FandolSong-Regular(0)/m/sc' undefined
(Font)              using `TU/FandolSong-Regular(0)/m/n' instead on input line 58.

[1


]

LaTeX Warning: Reference `fig:study_area' on page 2 undefined on input line 86.

File: fig/Study Area.pdf Graphic file (type pdf)
<use fig/Study Area.pdf>

LaTeX Warning: Reference `tab:datasets' on page 2 undefined on input line 97.

LaTeX Font Info:    Trying to load font information for U+msa on input line 105.
(C:\Program Files\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 105.
 (C:\Program Files\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Runaway argument?
{tabular> \end {table*} 
e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:131: Paragraph ended before \end  was complete.
<to be read again> 
                   \par 
l.131 
      
I suspect you've forgotten a `}', causing me to apply this
control sequence to too much text. How can we recover?
My plan is to forget the whole thing and hope for the best.


Underfull \hbox (badness 10000) in paragraph at lines 132--133
[]\TU/FandolSong-Regular(0)/b/n/7 橡 胶 树 数 据 集
 []


Underfull \hbox (badness 1502) in paragraph at lines 132--133
\TU/FandolSong-Regular(0)/m/n/7 的 实 验 林 区，|  涵
 []


Underfull \hbox (badness 1502) in paragraph at lines 132--133
\TU/FandolSong-Regular(0)/m/n/7 盖 萌 芽 期、|  生 长
 []


Underfull \hbox (badness 1603) in paragraph at lines 132--133
\TU/FandolSong-Regular(0)/m/n/7 占 训 练 集 \TU/lmr/m/n/7 4.7%\TU/FandolSong-Regular(0)/m/n/7 ，|
 []


Underfull \hbox (badness 10000) in paragraph at lines 136--137
[]\TU/FandolSong-Regular(0)/b/n/7 杨 梅 树 数 据 集
 []


Underfull \hbox (badness 10000) in paragraph at lines 136--137
\TU/FandolSong-Regular(0)/m/n/7 园  |（\TU/lmr/m/n/7 28°17’N |\TU/FandolSong-Regular(0)/m/n/7 –|
 []


Underfull \hbox (badness 10000) in paragraph at lines 136--137
\TU/lmr/m/n/7 28°19’N,
 []


Underfull \hbox (badness 10000) in paragraph at lines 136--137
\TU/lmr/m/n/7 120°26’E |\TU/FandolSong-Regular(0)/m/n/7 –|
 []


Underfull \hbox (badness 10000) in paragraph at lines 136--137
\TU/lmr/m/n/7 120°28’E\TU/FandolSong-Regular(0)/m/n/7 ） ，|
 []


Underfull \hbox (badness 10000) in paragraph at lines 138--139
\TU/FandolSong-Regular(0)/m/n/7 达 市  |（\TU/lmr/m/n/7 41°37’N,
 []


Underfull \hbox (badness 3579) in paragraph at lines 138--139
\TU/lmr/m/n/7 0°37’E\TU/FandolSong-Regular(0)/m/n/7 ）|  的 地 中
 []


Underfull \hbox (badness 10000) in paragraph at lines 140--141
\TU/FandolSong-Regular(0)/m/n/7 采 集 自 加 拿 大
 []


Underfull \hbox (badness 10000) in paragraph at lines 140--141
\TU/FandolSong-Regular(0)/m/n/7 利 特  |（\TU/lmr/m/n/7 45°59’N,
 []


LaTeX Warning: Reference `tab:phenology' on page 2 undefined on input line 142.


Underfull \hbox (badness 1515) in paragraph at lines 142--143
[]\TU/FandolSong-Regular(0)/m/n/7 表[]进 一 步 详 细
 []


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:144: LaTeX Error: Not in outer par mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.144 \begin{table}[htbp]
                         
You've lost some text.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Underfull \hbox (badness 4156) in paragraph at lines 167--168
\U/msb/m/n/7 R[]$ \TU/FandolSong-Regular(0)/m/n/7 首
 []


Overfull \hbox (173.94554pt too wide) detected at line 173
[][][][][]
 []


Underfull \hbox (badness 1502) in paragraph at lines 175--176
\TU/FandolSong-Regular(0)/m/n/7 后，|  实 部 和 虚 部
 []


Underfull \hbox (badness 4156) in paragraph at lines 175--176
\U/msb/m/n/7 R[]$ \TU/FandolSong-Regular(0)/m/n/7 的
 []


Overfull \hbox (31.77263pt too wide) in paragraph at lines 175--176
\OMS/cmsy/m/n/7 f\OML/cmm/m/it/7 F[]; F[]; F[]; F[]; F[]\OMS/cmsy/m/n/7 g$\TU/FandolSong-Regular(0)/m/n/7 ，|
 []


Underfull \hbox (badness 1502) in paragraph at lines 175--176
\TU/FandolSong-Regular(0)/m/n/7 积 层、|  批 归 一 化
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
[]\TU/FandolSong-Regular(0)/m/n/7 三 个 专 门 化 模
 []


Underfull \hbox (badness 5832) in paragraph at lines 177--178
\TU/FandolSong-Regular(0)/m/n/7 金 字 塔 的 输 出。|
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/lmr/m/n/7 GM-Mamba \TU/FandolSong-Regular(0)/m/n/7 模
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/FandolSong-Regular(0)/m/n/7 块 针 对 每 个 尺
 []


Overfull \hbox (0.96692pt too wide) in paragraph at lines 177--178
\U/msb/m/n/7 R[]$\TU/FandolSong-Regular(0)/m/n/7 ，|
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/FandolSong-Regular(0)/m/n/7 库 的 \TU/lmr/m/n/7 rearrange
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/FandolSong-Regular(0)/m/n/7 函 数 将 空 间 维
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/FandolSong-Regular(0)/m/n/7 度 重 塑 为 序
 []


Overfull \hbox (6.38364pt too wide) in paragraph at lines 177--178
\U/msb/m/n/7 R[]$\TU/FandolSong-Regular(0)/m/n/7 ，|
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/lmr/m/n/7 d_model=256\TU/FandolSong-Regular(0)/m/n/7 、|
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/lmr/m/n/7 n_layer=1\TU/FandolSong-Regular(0)/m/n/7 、|
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/lmr/m/n/7 d_state=4\TU/FandolSong-Regular(0)/m/n/7 、|
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/lmr/m/n/7 dt_rank=4 \TU/FandolSong-Regular(0)/m/n/7 的
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/lmr/m/n/7 Mamba \TU/FandolSong-Regular(0)/m/n/7 模 块
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/FandolSong-Regular(0)/m/n/7 进 行 长 程 依 赖
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/FandolSong-Regular(0)/m/n/7 建 模。| \TU/lmr/m/n/7 MASA-
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/lmr/m/n/7 Optimizer \TU/FandolSong-Regular(0)/m/n/7 模
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/FandolSong-Regular(0)/m/n/7 块 维 护 一 个 容
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/FandolSong-Regular(0)/m/n/7 量 为 \TU/lmr/m/n/7 100 \TU/FandolSong-Regular(0)/m/n/7 的
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/lmr/m/n/7 MemoryBuffer\TU/FandolSong-Regular(0)/m/n/7 ，|
 []


Underfull \hbox (badness 1502) in paragraph at lines 177--178
\TU/FandolSong-Regular(0)/m/n/7 特 征 表 示，|  通 过
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/lmr/m/n/7 iCaRLNet \TU/FandolSong-Regular(0)/m/n/7 网
 []


Underfull \hbox (badness 7981) in paragraph at lines 177--178
\TU/FandolSong-Regular(0)/m/n/7 因 子 $\OML/cmm/m/it/7 ^^K$\TU/FandolSong-Regular(0)/m/n/7 。| \TU/lmr/m/n/7 MPC-
 []


Underfull \hbox (badness 1502) in paragraph at lines 177--178
\TU/FandolSong-Regular(0)/m/n/7 网 络，|  输 入 归 一
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\OT1/cmr/m/n/7 [\OMS/cmsy/m/n/7 ^^@\OT1/cmr/m/n/7 1\OML/cmm/m/it/7 ; \OT1/cmr/m/n/7 1][]$\TU/FandolSong-Regular(0)/m/n/7 ，|  输 出
 []


Underfull \hbox (badness 10000) in paragraph at lines 177--178
\TU/FandolSong-Regular(0)/m/n/7 树 冠 存 在 概 率
 []


Underfull \hbox (badness 1502) in paragraph at lines 179--180
\TU/FandolSong-Regular(0)/m/n/7 过 程 如 下：|  给 定
 []


Underfull \hbox (badness 10000) in paragraph at lines 179--180
\TU/FandolSong-Regular(0)/m/n/7 先 通 过 拉 普 拉
 []


Overfull \hbox (31.77962pt too wide) in paragraph at lines 179--180
\OMS/cmsy/m/n/7 f\OML/cmm/m/it/7 F[]; F[]; F[]; F[]; F[]\OMS/cmsy/m/n/7 g$\TU/FandolSong-Regular(0)/m/n/7 ；|
 []


Underfull \hbox (badness 10000) in paragraph at lines 179--180
\TU/FandolSong-Regular(0)/m/n/7 然 后 \TU/lmr/m/n/7 GM-
 []


Underfull \hbox (badness 10000) in paragraph at lines 179--180
\TU/lmr/m/n/7 Mamba \TU/FandolSong-Regular(0)/m/n/7 模 块
 []


Overfull \hbox (31.77962pt too wide) in paragraph at lines 179--180
\OMS/cmsy/m/n/7 f\OML/cmm/m/it/7 F[]; F[]; F[]; F[]; F[]\OMS/cmsy/m/n/7 g$\TU/FandolSong-Regular(0)/m/n/7 ；|
 []


Underfull \hbox (badness 10000) in paragraph at lines 179--180
\TU/lmr/m/n/7 MASA-
 []


Underfull \hbox (badness 10000) in paragraph at lines 179--180
\TU/lmr/m/n/7 Optimizer \TU/FandolSong-Regular(0)/m/n/7 根
 []


Underfull \hbox (badness 1430) in paragraph at lines 179--180
\TU/FandolSong-Regular(0)/m/n/7 算 遗 忘 因 子 $\OML/cmm/m/it/7 ^^K[]$\TU/FandolSong-Regular(0)/m/n/7 ；|
 []


Underfull \hbox (badness 10000) in paragraph at lines 179--180
\U/msb/m/n/7 R[]$\TU/FandolSong-Regular(0)/m/n/7 ；|
 []


Underfull \hbox (badness 10000) in paragraph at lines 179--180
\TU/FandolSong-Regular(0)/m/n/7 最 终 通 过 加 权
 []


Underfull \hbox (badness 10000) in paragraph at lines 179--180
\TU/FandolSong-Regular(0)/m/n/7 融 合 生 成 分 割
 []


Underfull \hbox (badness 10000) in paragraph at lines 179--180
\OML/cmm/m/it/7 W[]$\TU/FandolSong-Regular(0)/m/n/7 ，|  其
 []


Underfull \hbox (badness 10000) in paragraph at lines 179--180
\TU/FandolSong-Regular(0)/m/n/7 中 $\OML/cmm/m/it/7 F[]$
 []


Underfull \hbox (badness 10000) in paragraph at lines 179--180
\TU/FandolSong-Regular(0)/m/n/7 为 \TU/lmr/m/n/7 GM-Mamba
 []


Underfull \hbox (badness 7907) in paragraph at lines 179--180
\TU/FandolSong-Regular(0)/m/n/7 增 强 后 的 特 征，|
 []


Underfull \hbox (badness 10000) in paragraph at lines 181--181
 \TU/lmr/m/it/10 B. GM-
 []


Underfull \hbox (badness 10000) in paragraph at lines 181--181
\TU/lmr/m/it/10 Mamba \TU/FandolSong-Regular(0)/m/it/10 边
 []


Underfull \hbox (badness 10000) in paragraph at lines 183--184
[]\TU/lmr/m/n/7 GM-Mamba \TU/FandolSong-Regular(0)/m/n/7 模
 []


Underfull \hbox (badness 1502) in paragraph at lines 183--184
\TU/FandolSong-Regular(0)/m/n/7 关 系。|  橡 胶 树 成
 []


Underfull \hbox (badness 10000) in paragraph at lines 183--184
\TU/FandolSong-Regular(0)/m/n/7 征。| \TU/lmr/m/n/7 GM-Mamba
 []


Underfull \hbox (badness 10000) in paragraph at lines 183--184
\TU/FandolSong-Regular(0)/m/n/7 模 块 对 特 征 金
 []


Underfull \hbox (badness 10000) in paragraph at lines 183--184
\OMS/cmsy/m/n/7 f\OML/cmm/m/it/7 F[]; F[]; F[]\OMS/cmsy/m/n/7 g$
 []


Underfull \hbox (badness 1502) in paragraph at lines 183--184
\TU/FandolSong-Regular(0)/m/n/7 细 节 丢 失。|  每 个
 []


Underfull \hbox (badness 10000) in paragraph at lines 183--184
\U/msb/m/n/7 R[]$
 []


Overfull \hbox (81.65863pt too wide) detected at line 190
[][][][][]
 []


Overfull \hbox (11.79616pt too wide) detected at line 201
[][][][][]
 []


Overfull \hbox (79.67477pt too wide) detected at line 207
[][][]\OT1/cmr/m/n/7 [^^A\OML/cmm/m/it/7 ; B; C\OT1/cmr/m/n/7 ] = []([](\OML/cmm/m/it/7 X\OT1/cmr/m/n/7 )\OML/cmm/m/it/7 ; \OT1/cmr/m/n/7 [\OML/cmm/m/it/7 R; N; N\OT1/cmr/m/n/7 ])
 []


Underfull \hbox (badness 10000) in paragraph at lines 209--210
[]\TU/FandolSong-Regular(0)/m/n/7 其 中
 []


Underfull \hbox (badness 10000) in paragraph at lines 209--210
\U/msb/m/n/7 R[]$ \TU/FandolSong-Regular(0)/m/n/7 和
 []


Overfull \hbox (19.33304pt too wide) in paragraph at lines 209--210
[]\OT1/cmr/m/n/7 ([](^^A))$\TU/FandolSong-Regular(0)/m/n/7 。| 
 []


Overfull \hbox (1.17749pt too wide) detected at line 215
[][][]\OML/cmm/m/it/7 A \OT1/cmr/m/n/7 = \OMS/cmsy/m/n/7 ^^@ []\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 A[]\OT1/cmr/m/n/7 )
 []


Overfull \hbox (27.51846pt too wide) detected at line 224
[][][][][]
 []


Underfull \hbox (badness 10000) in paragraph at lines 226--227
[]\TU/FandolSong-Regular(0)/m/n/7 矩 阵 指 数 通 过
 []


Underfull \hbox (badness 3108) in paragraph at lines 226--227
\TU/lmr/m/n/7 Padé \TU/FandolSong-Regular(0)/m/n/7 近 似 或 特
 []


Underfull \hbox (badness 6316) in paragraph at lines 226--227
\TU/FandolSong-Regular(0)/m/n/7 征 值 分 解 计 算，|
 []


Underfull \hbox (badness 10000) in paragraph at lines 226--227
\TU/FandolSong-Regular(0)/m/n/7 确 保 数 值 稳 定
 []


Underfull \hbox (badness 1502) in paragraph at lines 226--227
\TU/FandolSong-Regular(0)/m/n/7 性。|  核 心 的 选 择
 []


Underfull \hbox (badness 3354) in paragraph at lines 226--227
\TU/lmr/m/n/7 Mamba \TU/FandolSong-Regular(0)/m/n/7 的 并 行
 []


Underfull \hbox (badness 1502) in paragraph at lines 226--227
\TU/FandolSong-Regular(0)/m/n/7 实 现，|  通 过 关 联
 []


Overfull \hbox (69.99069pt too wide) detected at line 230
[][][]\OML/cmm/m/it/7 Y \OT1/cmr/m/n/7 = [](\OML/cmm/m/it/7 X; \OT1/cmr/m/n/7 ^^A\OML/cmm/m/it/7 ; A; B; C; D\OT1/cmr/m/n/7 )
 []


Underfull \hbox (badness 1502) in paragraph at lines 232--233
\TU/FandolSong-Regular(0)/m/n/7 赖，|  时 间 复 杂 度
 []


Underfull \hbox (badness 10000) in paragraph at lines 234--235
[]\TU/FandolSong-Regular(0)/m/n/7 完 整 的 \TU/lmr/m/n/7 GM-
 []


Overfull \hbox (79.05133pt too wide) detected at line 241
[][][][][]
 []


Overfull \hbox (26.91208pt too wide) detected at line 247
[][][]\OML/cmm/m/it/7 F[] \OT1/cmr/m/n/7 = \OML/cmm/m/it/7 F[] \OT1/cmr/m/n/7 + \OML/cmm/m/it/7 ^^M \OMS/cmsy/m/n/7 ^^A \OML/cmm/m/it/7 F[]
 []


Underfull \hbox (badness 10000) in paragraph at lines 251--251
 \TU/lmr/m/it/10 C. MPC-
 []


Overfull \hbox (59.74004pt too wide) detected at line 259
[][][]\OMS/cmsy/m/n/7 r[]\OML/cmm/m/it/7 u\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 x; y\OT1/cmr/m/n/7 ) = [] + [] = \OML/cmm/m/it/7 f\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 x; y\OT1/cmr/m/n/7 )
 []


Underfull \hbox (badness 2005) in paragraph at lines 261--262
[]\TU/FandolSong-Regular(0)/m/n/7 其 中 $\OML/cmm/m/it/7 u\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 x; y\OT1/cmr/m/n/7 )$ \TU/FandolSong-Regular(0)/m/n/7 表
 []


Overfull \hbox (92.95786pt too wide) detected at line 269
[][][][][]
 []


Underfull \hbox (badness 10000) in paragraph at lines 271--272
[]\TU/FandolSong-Regular(0)/m/n/7 其 中 网 络 结
 []


Underfull \hbox (badness 10000) in paragraph at lines 271--272
\TU/FandolSong-Regular(0)/m/n/7 构 为 $\U/msb/m/n/7 R[] \OMS/cmsy/m/n/7 !
 []


Underfull \hbox (badness 1540) in paragraph at lines 271--272
\TU/FandolSong-Regular(0)/m/n/7 数 $[]\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 x\OT1/cmr/m/n/7 ) =
 []


Underfull \hbox (badness 6658) in paragraph at lines 271--272
\OML/cmm/m/it/7 x \OMS/cmsy/m/n/7 ^^A \OML/cmm/m/it/7 ^^[\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 x\OT1/cmr/m/n/7 )$ \TU/FandolSong-Regular(0)/m/n/7 以 保
 []


Underfull \hbox (badness 10000) in paragraph at lines 271--272
\TU/FandolSong-Regular(0)/m/n/7 证 高 阶 导 数 的
 []


Underfull \hbox (badness 1502) in paragraph at lines 271--272
\TU/FandolSong-Regular(0)/m/n/7 连 续 性，|  输 出 层
 []


Underfull \hbox (badness 10000) in paragraph at lines 271--272
\TU/FandolSong-Regular(0)/m/n/7 使 用 \TU/lmr/m/n/7 Sigmoid
 []


Underfull \hbox (badness 7012) in paragraph at lines 271--272
\TU/FandolSong-Regular(0)/m/n/7 函 数 $\OML/cmm/m/it/7 ^^[\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 x\OT1/cmr/m/n/7 ) =
 []


Underfull \hbox (badness 10000) in paragraph at lines 280--281
[]\TU/FandolSong-Regular(0)/m/n/7 物 理 约 束 通 过
 []


Underfull \hbox (badness 2150) in paragraph at lines 280--281
\OML/cmm/m/it/7 u\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 x; y\OT1/cmr/m/n/7 )$\TU/FandolSong-Regular(0)/m/n/7 ，|  计 算 其
 []


Overfull \hbox (42.71906pt too wide) detected at line 286
[][][][][]
 []


Overfull \hbox (67.8627pt too wide) detected at line 292
[][][]\OMS/cmsy/m/n/7 R[]\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 x; y\OT1/cmr/m/n/7 ) = \OML/cmm/m/it/7 u[] \OT1/cmr/m/n/7 + \OML/cmm/m/it/7 u[] \OT1/cmr/m/n/7 + \OML/cmm/m/it/7 f\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 x; y\OT1/cmr/m/n/7 )
 []


Overfull \hbox (130.58275pt too wide) detected at line 300
[][][][][]
 []


Underfull \hbox (badness 1502) in paragraph at lines 304--305
\TU/FandolSong-Regular(0)/m/n/7 效 性，|  模 块 采 用
 []


Underfull \hbox (badness 1502) in paragraph at lines 304--305
\TU/FandolSong-Regular(0)/m/n/7 策 略。|  在 三 个 不
 []


Overfull \hbox (7.92754pt too wide) in paragraph at lines 304--305
\OMS/cmsy/m/n/7 f\OML/cmm/m/it/7 H\OT1/cmr/m/n/7 /4\OML/cmm/m/it/7 ; H\OT1/cmr/m/n/7 /8\OML/cmm/m/it/7 ; H\OT1/cmr/m/n/7 /16\OMS/cmsy/m/n/7 g$
 []


Overfull \hbox (100.72261pt too wide) detected at line 308
[][][]\OMS/cmsy/m/n/7 L[] \OT1/cmr/m/n/7 = [] \OML/cmm/m/it/7 w[][] [] \OMS/cmsy/m/n/7 jR[]\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 x[]; y[]\OT1/cmr/m/n/7 )\OMS/cmsy/m/n/7 j[]
 []


Underfull \hbox (badness 10000) in paragraph at lines 310--311
[]\TU/FandolSong-Regular(0)/m/n/7 其 中 $\OML/cmm/m/it/7 w[] \OT1/cmr/m/n/7 =
 []


Underfull \hbox (badness 7362) in paragraph at lines 310--311
\OT1/cmr/m/n/7 [0\OML/cmm/m/it/7 :\OT1/cmr/m/n/7 5\OML/cmm/m/it/7 ; \OT1/cmr/m/n/7 0\OML/cmm/m/it/7 :\OT1/cmr/m/n/7 3\OML/cmm/m/it/7 ; \OT1/cmr/m/n/7 0\OML/cmm/m/it/7 :\OT1/cmr/m/n/7 2]$ \TU/FandolSong-Regular(0)/m/n/7 为
 []


Overfull \hbox (123.84671pt too wide) detected at line 322
[][][][][]
 []


Overfull \hbox (42.95302pt too wide) detected at line 329
[][][]\OML/cmm/m/it/7 F[] \OT1/cmr/m/n/7 = \OML/cmm/m/it/7 F \OMS/cmsy/m/n/7 ^^L \OML/cmm/m/it/7 W[]
 []


Overfull \hbox (105.12453pt too wide) detected at line 335
[][][]\OMS/cmsy/m/n/7 L[] \OT1/cmr/m/n/7 = \OMS/cmsy/m/n/7 L[] \OT1/cmr/m/n/7 + \OML/cmm/m/it/7 ^^U[]\OMS/cmsy/m/n/7 L[] \OT1/cmr/m/n/7 + \OML/cmm/m/it/7 ^^U[]\OMS/cmsy/m/n/7 L[]
 []


Underfull \hbox (badness 10000) in paragraph at lines 339--339
 \TU/lmr/m/it/10 D. MASA-
 []


Underfull \hbox (badness 10000) in paragraph at lines 341--342
[]\TU/lmr/m/n/7 MASA-
 []


Underfull \hbox (badness 10000) in paragraph at lines 341--342
\TU/lmr/m/n/7 Optimizer \TU/FandolSong-Regular(0)/m/n/7 模
 []


Overfull \hbox (70.1821pt too wide) detected at line 347
[][][]\OMS/cmsy/m/n/7 L[] \OT1/cmr/m/n/7 = \OML/cmm/m/it/7 ^^K\OMS/cmsy/m/n/7 L[] \OT1/cmr/m/n/7 + (1 \OMS/cmsy/m/n/7 ^^@ \OML/cmm/m/it/7 ^^K\OT1/cmr/m/n/7 )\OMS/cmsy/m/n/7 L[]
 []


Underfull \hbox (badness 7907) in paragraph at lines 349--350
\TU/FandolSong-Regular(0)/m/n/7 前 时 相 的 损 失，|
 []


Overfull \hbox (48.97382pt too wide) detected at line 356
[][][][][]
 []


Underfull \hbox (badness 1502) in paragraph at lines 358--359
\TU/FandolSong-Regular(0)/m/n/7 失。|  为 了 有 效 求
 []


Underfull \hbox (badness 10000) in paragraph at lines 358--359
|\TU/FandolSong-Regular(0)/m/n/7 （\TU/lmr/m/n/7 SimulatedAn-
 []


Underfull \hbox (badness 4316) in paragraph at lines 358--359
\TU/lmr/m/n/7 nealing\TU/FandolSong-Regular(0)/m/n/7 ） 、|  强
 []


Underfull \hbox (badness 10000) in paragraph at lines 358--359
\TU/FandolSong-Regular(0)/m/n/7 化 学 习 智 能 体
 []


Underfull \hbox (badness 10000) in paragraph at lines 358--359
|\TU/FandolSong-Regular(0)/m/n/7 （\TU/lmr/m/n/7 QLearningA-
 []


Underfull \hbox (badness 10000) in paragraph at lines 360--361
[]\TU/lmr/m/n/7 MASA-
 []


Underfull \hbox (badness 10000) in paragraph at lines 360--361
\TU/lmr/m/n/7 Optimizer \TU/FandolSong-Regular(0)/m/n/7 采
 []


Underfull \hbox (badness 5274) in paragraph at lines 360--361
\TU/FandolSong-Regular(0)/m/n/7 在 当 前 解 $\OML/cmm/m/it/7 ^^K[]$
 []


Overfull \hbox (60.71146pt too wide) detected at line 364
[][][]\OML/cmm/m/it/7 P[] \OT1/cmr/m/n/7 = [] []
 []


Underfull \hbox (badness 10000) in paragraph at lines 366--367
[]\TU/FandolSong-Regular(0)/m/n/7 其 中 $\OT1/cmr/m/n/7 ^^A\OML/cmm/m/it/7 E \OT1/cmr/m/n/7 =
 []


Overfull \hbox (11.8632pt too wide) in paragraph at lines 366--367
\OMS/cmsy/m/n/7 L[]\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 ^^K[]\OT1/cmr/m/n/7 ) \OMS/cmsy/m/n/7 ^^@
 []


Overfull \hbox (4.41472pt too wide) in paragraph at lines 366--367
\OMS/cmsy/m/n/7 L[]\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 ^^K[]\OT1/cmr/m/n/7 )$\TU/FandolSong-Regular(0)/m/n/7 ，|
 []


Overfull \hbox (84.9671pt too wide) detected at line 375
[][][][][]
 []


Overfull \hbox (80.99031pt too wide) detected at line 381
[][][]\OML/cmm/m/it/7 r[] \OT1/cmr/m/n/7 = \OMS/cmsy/m/n/7 ^^@L[]\OT1/cmr/m/n/7 (\OML/cmm/m/it/7 ^^K[]\OT1/cmr/m/n/7 ) \OMS/cmsy/m/n/7 ^^@ \OML/cmm/m/it/7 ^^U[]\OMS/cmsy/m/n/7 j\OML/cmm/m/it/7 ^^K[] \OMS/cmsy/m/n/7 ^^@ \OML/cmm/m/it/7 ^^K[]\OMS/cmsy/m/n/7 j
 []


Underfull \hbox (badness 4013) in paragraph at lines 383--384
[]\TU/FandolSong-Regular(0)/m/n/7 其 中 $\OML/cmm/m/it/7 ^^U[] \OT1/cmr/m/n/7 =
 []


Underfull \hbox (badness 3942) in paragraph at lines 385--386
\TU/lmr/m/n/7 100% \TU/FandolSong-Regular(0)/m/n/7 迭 代）|  使
 []


Underfull \hbox (badness 1502) in paragraph at lines 385--386
\TU/FandolSong-Regular(0)/m/n/7 局 部 精 调，|  种 群
 []


Underfull \hbox (badness 1721) in paragraph at lines 385--386
\TU/FandolSong-Regular(0)/m/n/7 规 模 设 置 为 \TU/lmr/m/n/7 20\TU/FandolSong-Regular(0)/m/n/7 ，|
 []


Overfull \hbox (21.91774pt too wide) in paragraph at lines 385--386
\OMS/cmsy/m/n/7 ^^@[]\OT1/cmr/m/n/7 (\OMS/cmsy/m/n/7 L[]\OT1/cmr/m/n/7 [\OMS/cmsy/m/n/7 ^^@\OT1/cmr/m/n/7 10 :
 []


Overfull \hbox (94.54979pt too wide) detected at line 392
[][][][][]
 []


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:397: LaTeX Error: Not in outer par mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.397 \begin{algorithm}[htbp]
                             
You've lost some text.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

Package hyperref Info: bookmark level for unknown algorithm defaults to 0 on input line 398.

Underfull \hbox (badness 1502) in paragraph at lines 431--432
\TU/FandolSong-Regular(0)/m/n/7 采 样 策 略，|  维 护
 []


Underfull \hbox (badness 10000) in paragraph at lines 431--432
\TU/lmr/m/n/7 MemoryBuffer
 []


Underfull \hbox (badness 1502) in paragraph at lines 431--432
\TU/FandolSong-Regular(0)/m/n/7 特 征 表 示。|  对 于
 []


Overfull \hbox (84.90817pt too wide) detected at line 438
[][][][][]
 []


Underfull \hbox (badness 7907) in paragraph at lines 440--441
\TU/FandolSong-Regular(0)/m/n/7 时 相 变 异 度 量，|
 []


Underfull \hbox (badness 3343) in paragraph at lines 442--443
\TU/lmr/m/n/7 Optimizer \TU/FandolSong-Regular(0)/m/n/7 机 制
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
[]\TU/FandolSong-Regular(0)/m/n/7 为 全 面 评 估
 []


Underfull \hbox (badness 1502) in paragraph at lines 449--450
\TU/FandolSong-Regular(0)/m/n/7 不 同 物 种、|  物 候
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/lmr/m/n/7 MMDetection
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/FandolSong-Regular(0)/m/n/7 框 架 内 实 现 的
 []


Underfull \hbox (badness 3219) in paragraph at lines 449--450
\TU/lmr/m/n/7 11 \TU/FandolSong-Regular(0)/m/n/7 个 主 流 实 例
 []


Underfull \hbox (badness 1502) in paragraph at lines 449--450
\TU/FandolSong-Regular(0)/m/n/7 对 比 分 析，|  包 括
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/lmr/m/n/7 CascadeMask
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/lmr/m/n/7 R-CNN\TU/FandolSong-Regular(0)/m/n/7 、| \TU/lmr/m/n/7 Swin
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/lmr/m/n/7 Transformer\TU/FandolSong-Regular(0)/m/n/7 、|
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/lmr/m/n/7 HTC\TU/FandolSong-Regular(0)/m/n/7 、|
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/lmr/m/n/7 YOLACT\TU/FandolSong-Regular(0)/m/n/7 、|
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/lmr/m/n/7 YOLOv8\TU/FandolSong-Regular(0)/m/n/7 、|
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/lmr/m/n/7 QueryInst\TU/FandolSong-Regular(0)/m/n/7 、|
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/lmr/m/n/7 Mask2Former\TU/FandolSong-Regular(0)/m/n/7 、|
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/lmr/m/n/7 SOLOv2 \TU/FandolSong-Regular(0)/m/n/7 等 通
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/FandolSong-Regular(0)/m/n/7 用 模 型，|  以 及
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/lmr/m/n/7 DetecTree2\TU/FandolSong-Regular(0)/m/n/7 、|
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/lmr/m/n/7 MRT\TU/FandolSong-Regular(0)/m/n/7 、|
 []


Underfull \hbox (badness 10000) in paragraph at lines 449--450
\TU/lmr/m/n/7 TreeCrown-
 []

Missing character: There is no ⁻ in font [lmroman7-regular]:mapping=tex-text;!
Missing character: There is no ⁴ in font [lmroman7-regular]:mapping=tex-text;!

Underfull \hbox (badness 10000) in paragraph at lines 451--452
\TU/lmr/m/n/7 Platinum 8362
 []


Underfull \hbox (badness 3838) in paragraph at lines 451--452
\TU/lmr/m/n/7 Ubuntu 18.04.6
 []


Underfull \hbox (badness 10000) in paragraph at lines 451--452
\TU/lmr/m/n/7 LTS\TU/FandolSong-Regular(0)/m/n/7 、| \TU/lmr/m/n/7 Python
 []


Underfull \hbox (badness 1584) in paragraph at lines 451--452
\TU/lmr/m/n/7 3.7 \TU/FandolSong-Regular(0)/m/n/7 和 \TU/lmr/m/n/7 PyTorch
 []


Underfull \hbox (badness 3019) in paragraph at lines 451--452
\TU/FandolSong-Regular(0)/m/n/7 性，| \TU/lmr/m/n/7 CSAF \TU/FandolSong-Regular(0)/m/n/7 和 基
 []


Overfull \hbox (15.76913pt too wide) detected at line 465
[][][][] \OT1/cmr/m/n/7 = [] [] [](\OML/cmm/m/it/7 t\OT1/cmr/m/n/7 )
 []


Overfull \hbox (19.29276pt too wide) detected at line 471
[][][][] \OT1/cmr/m/n/7 = []
 []


Overfull \hbox (9.53474pt too wide) detected at line 475
[][][][] \OT1/cmr/m/n/7 = []
 []


Underfull \hbox (badness 10000) in paragraph at lines 477--478
[]\TU/FandolSong-Regular(0)/m/n/7 其 中，| \TU/lmr/m/n/7 TP\TU/FandolSong-Regular(0)/m/n/7 、| \TU/lmr/m/n/7 FP\TU/FandolSong-Regular(0)/m/n/7 、|
 []


LaTeX Warning: Reference `tab:ablation' on page 2 undefined on input line 483.


Underfull \hbox (badness 1502) in paragraph at lines 483--484
\TU/FandolSong-Regular(0)/m/n/7 和 协 作 机 制，|  在
 []


Underfull \hbox (badness 3590) in paragraph at lines 483--484
\TU/FandolSong-Regular(0)/m/n/7 实 验。|  如 表[]所
 []


Underfull \hbox (badness 10000) in paragraph at lines 483--484
\TU/FandolSong-Regular(0)/m/n/7 能。| \TU/lmr/m/n/7 GM-Mamba
 []


Underfull \hbox (badness 10000) in paragraph at lines 483--484
\TU/FandolSong-Regular(0)/m/n/7 增 强 将 \TU/lmr/m/n/7 AP50
 []


Underfull \hbox (badness 5175) in paragraph at lines 483--484
\TU/FandolSong-Regular(0)/m/n/7 提 升 至 \TU/lmr/m/n/7 72.59%
 []


Underfull \hbox (badness 10000) in paragraph at lines 483--484
|\TU/FandolSong-Regular(0)/m/n/7 （\TU/lmr/m/n/7 +2.07%\TU/FandolSong-Regular(0)/m/n/7 ） ，|
 []


Underfull \hbox (badness 10000) in paragraph at lines 483--484
\TU/lmr/m/n/7 MASA-
 []


Underfull \hbox (badness 10000) in paragraph at lines 483--484
\TU/lmr/m/n/7 Optimizer \TU/FandolSong-Regular(0)/m/n/7 通
 []


Underfull \hbox (badness 10000) in paragraph at lines 483--484
\TU/FandolSong-Regular(0)/m/n/7 过 动 态 特 征 适
 []


Underfull \hbox (badness 7907) in paragraph at lines 483--484
\TU/FandolSong-Regular(0)/m/n/7 应 将 \TU/lmr/m/n/7 AP50 \TU/FandolSong-Regular(0)/m/n/7 提
 []


Underfull \hbox (badness 10000) in paragraph at lines 483--484
\TU/FandolSong-Regular(0)/m/n/7 升 至 \TU/lmr/m/n/7 72.81%
 []


Underfull \hbox (badness 10000) in paragraph at lines 483--484
|\TU/FandolSong-Regular(0)/m/n/7 （\TU/lmr/m/n/7 +2.29%\TU/FandolSong-Regular(0)/m/n/7 ） ，|
 []


Underfull \hbox (badness 10000) in paragraph at lines 483--484
\TU/lmr/m/n/7 MPC-Poisson
 []


Underfull \hbox (badness 10000) in paragraph at lines 483--484
\TU/FandolSong-Regular(0)/m/n/7 通 过 物 理 约 束
 []


Underfull \hbox (badness 10000) in paragraph at lines 483--484
\TU/FandolSong-Regular(0)/m/n/7 将 \TU/lmr/m/n/7 AP50 \TU/FandolSong-Regular(0)/m/n/7 提
 []


Underfull \hbox (badness 10000) in paragraph at lines 483--484
\TU/FandolSong-Regular(0)/m/n/7 升 至 \TU/lmr/m/n/7 72.01%
 []


Underfull \hbox (badness 10000) in paragraph at lines 485--486
\TU/lmr/m/n/7 GM-Mamba
 []


Underfull \hbox (badness 10000) in paragraph at lines 485--486
\TU/lmr/m/n/7 + MASA-
 []


Underfull \hbox (badness 10000) in paragraph at lines 485--486
\TU/lmr/m/n/7 Optimizer \TU/FandolSong-Regular(0)/m/n/7 在
 []


Underfull \hbox (badness 10000) in paragraph at lines 485--486
\TU/lmr/m/n/7 GM-Mamba +
 []


Underfull \hbox (badness 1430) in paragraph at lines 485--486
\TU/FandolSong-Regular(0)/m/n/7 场 景 下 将 \TU/lmr/m/n/7 AP75
 []


Underfull \hbox (badness 5175) in paragraph at lines 485--486
\TU/FandolSong-Regular(0)/m/n/7 提 升 至 \TU/lmr/m/n/7 73.89%
 []


Underfull \hbox (badness 10000) in paragraph at lines 485--486
|\TU/FandolSong-Regular(0)/m/n/7 （\TU/lmr/m/n/7 +3.37%\TU/FandolSong-Regular(0)/m/n/7 ） 。|
 []


Underfull \hbox (badness 10000) in paragraph at lines 485--486
\TU/lmr/m/n/7 CDPO-E \TU/FandolSong-Regular(0)/m/n/7 引
 []


Underfull \hbox (badness 10000) in paragraph at lines 485--486
\TU/FandolSong-Regular(0)/m/n/7 擎  |（\TU/lmr/m/n/7 MASA-
 []


Underfull \hbox (badness 10000) in paragraph at lines 485--486
\TU/lmr/m/n/7 Optimizer +
 []


Underfull \hbox (badness 10000) in paragraph at lines 485--486
\TU/lmr/m/n/7 MPC-Poisson\TU/FandolSong-Regular(0)/m/n/7 ）|
 []


Underfull \hbox (badness 1502) in paragraph at lines 485--486
\TU/FandolSong-Regular(0)/m/n/7 型  |（集 成 所 有 三
 []


Underfull \hbox (badness 1502) in paragraph at lines 485--486
\TU/FandolSong-Regular(0)/m/n/7 个 模 块）|  取 得 最
 []


Underfull \hbox (badness 10000) in paragraph at lines 485--486
\TU/FandolSong-Regular(0)/m/n/7 优 结 果，| \TU/lmr/m/n/7 AP50
 []


Underfull \hbox (badness 10000) in paragraph at lines 485--486
\TU/FandolSong-Regular(0)/m/n/7 达 到 \TU/lmr/m/n/7 76.63%
 []


Underfull \hbox (badness 10000) in paragraph at lines 485--486
\TU/FandolSong-Regular(0)/m/n/7 达 到 \TU/lmr/m/n/7 44.11%
 []


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:489: LaTeX Error: Not in outer par mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.489 \begin{table*}[htbp]
                          
You've lost some text.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Underfull \hbox (badness 3343) in paragraph at lines 514--515
\TU/lmr/m/n/7 Optimizer \TU/FandolSong-Regular(0)/m/n/7 中 三
 []


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:516: LaTeX Error: Not in outer par mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.516 \begin{table*}[htbp]
                          
You've lost some text.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

Missing character: There is no α in font [lmroman8-regular]:mapping=tex-text;!
Missing character: There is no α in font [lmroman7-regular]:mapping=tex-text;!

Underfull \hbox (badness 10000) in paragraph at lines 537--538
[]\TU/FandolSong-Regular(0)/m/n/7 实 验 结 果 表
 []


Underfull \hbox (badness 1502) in paragraph at lines 537--538
\TU/FandolSong-Regular(0)/m/n/7 明，|  原 始 设 计 的
 []


Underfull \hbox (badness 10000) in paragraph at lines 537--538
\TU/lmr/m/n/7 SA→RL→GA
 []


Underfull \hbox (badness 10000) in paragraph at lines 537--538
\TU/lmr/m/n/7 40.523±1.32%\TU/FandolSong-Regular(0)/m/n/7 ，|
 []


Underfull \hbox (badness 3396) in paragraph at lines 539--540
[]\TU/FandolSong-Regular(0)/m/n/7 从 \TU/lmr/m/n/7 6 \TU/FandolSong-Regular(0)/m/n/7 种 排 列 组
 []


Underfull \hbox (badness 1502) in paragraph at lines 539--540
\TU/FandolSong-Regular(0)/m/n/7 律 性：|  以 模 拟 退
 []


Underfull \hbox (badness 10000) in paragraph at lines 539--540
\TU/lmr/m/n/7 SA→RL→GA
 []


Underfull \hbox (badness 10000) in paragraph at lines 539--540
\TU/FandolSong-Regular(0)/m/n/7 和
 []


Underfull \hbox (badness 10000) in paragraph at lines 539--540
\TU/lmr/m/n/7 SA→GA→RL
 []


Underfull \hbox (badness 10000) in paragraph at lines 539--540
\TU/FandolSong-Regular(0)/m/n/7 分 别 取 得
 []


Underfull \hbox (badness 10000) in paragraph at lines 539--540
\TU/lmr/m/n/7 40.523% \TU/FandolSong-Regular(0)/m/n/7 和
 []


Underfull \hbox (badness 10000) in paragraph at lines 539--540
\TU/lmr/m/n/7 RL→SA→GA
 []


Underfull \hbox (badness 3364) in paragraph at lines 539--540
\TU/FandolSong-Regular(0)/m/n/7 序 列 \TU/lmr/m/n/7 (38.076%)
 []


Underfull \hbox (badness 1502) in paragraph at lines 539--540
\TU/FandolSong-Regular(0)/m/n/7 果。|  以 遗 传 算 法
 []


Underfull \hbox (badness 10000) in paragraph at lines 539--540
\TU/lmr/m/n/7 GA→SA→RL
 []


Underfull \hbox (badness 10000) in paragraph at lines 539--540
\TU/FandolSong-Regular(0)/m/n/7 和
 []


Underfull \hbox (badness 10000) in paragraph at lines 539--540
\TU/lmr/m/n/7 GA→RL→SA
 []


Underfull \hbox (badness 10000) in paragraph at lines 539--540
\TU/FandolSong-Regular(0)/m/n/7 的 \TU/lmr/m/n/7 AP \TU/FandolSong-Regular(0)/m/n/7 分 别
 []


Underfull \hbox (badness 10000) in paragraph at lines 539--540
\TU/FandolSong-Regular(0)/m/n/7 为 \TU/lmr/m/n/7 38.294% \TU/FandolSong-Regular(0)/m/n/7 和
 []


Underfull \hbox (badness 10000) in paragraph at lines 539--540
\TU/lmr/m/n/7 37.635%\TU/FandolSong-Regular(0)/m/n/7 ，|  而
 []


Underfull \hbox (badness 10000) in paragraph at lines 539--540
\TU/lmr/m/n/7 RL→GA→SA
 []


Underfull \hbox (badness 5331) in paragraph at lines 539--540
\TU/FandolSong-Regular(0)/m/n/7 序 列 的 \TU/lmr/m/n/7 AP \TU/FandolSong-Regular(0)/m/n/7 为
 []


Underfull \hbox (badness 10000) in paragraph at lines 541--542
[]\TU/FandolSong-Regular(0)/m/n/7 从 收 敛 稳 定
 []


Underfull \hbox (badness 10000) in paragraph at lines 541--542
\TU/FandolSong-Regular(0)/m/n/7 性 角 度 分 析，|
 []


Underfull \hbox (badness 10000) in paragraph at lines 541--542
\TU/lmr/m/n/7 SA→RL→GA
 []


Underfull \hbox (badness 1502) in paragraph at lines 541--542
\TU/FandolSong-Regular(0)/m/n/7 鲁 棒 性。|  不 同 序
 []


Underfull \hbox (badness 10000) in paragraph at lines 541--542
\TU/lmr/m/n/7 SA→GA→RL
 []


Underfull \hbox (badness 1502) in paragraph at lines 541--542
\TU/FandolSong-Regular(0)/m/n/7 优，|  但 标 准 差 相
 []


Underfull \hbox (badness 2538) in paragraph at lines 541--542
\TU/FandolSong-Regular(0)/m/n/7 对 较 大 \TU/lmr/m/n/7 (1.89%-
 []


Underfull \hbox (badness 10000) in paragraph at lines 541--542
\TU/lmr/m/n/7 2.14%)\TU/FandolSong-Regular(0)/m/n/7 ，|  而
 []


Underfull \hbox (badness 10000) in paragraph at lines 541--542
\TU/lmr/m/n/7 RL→SA→GA
 []


Underfull \hbox (badness 10000) in paragraph at lines 541--542
\TU/FandolSong-Regular(0)/m/n/7 序 列 在 保 持 较
 []


Underfull \hbox (badness 10000) in paragraph at lines 541--542
\TU/FandolSong-Regular(0)/m/n/7 好 性 能 的 同 时
 []


Underfull \hbox (badness 10000) in paragraph at lines 541--542
\TU/FandolSong-Regular(0)/m/n/7 维 持 了 相 对 稳
 []


Underfull \hbox (badness 5832) in paragraph at lines 541--542
\TU/FandolSong-Regular(0)/m/n/7 定 的 收 敛 特 性。|
 []


Underfull \hbox (badness 10000) in paragraph at lines 541--542
\TU/lmr/m/n/7 RL→GA→SA
 []


Underfull \hbox (badness 10000) in paragraph at lines 541--542
\TU/lmr/m/n/7 (2.84%-3.17%)\TU/FandolSong-Regular(0)/m/n/7 ，|
 []


Underfull \hbox (badness 10000) in paragraph at lines 545--545
 \TU/lmr/m/it/10 D. GM-
 []


Underfull \hbox (badness 4266) in paragraph at lines 547--548
[]\TU/FandolSong-Regular(0)/m/n/7 为 了 验 证 \TU/lmr/m/n/7 GM-
 []


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:549: LaTeX Error: Not in outer par mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.549 \begin{table*}[htbp]
                          
You've lost some text.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Underfull \hbox (badness 6316) in paragraph at lines 577--578
[]\TU/FandolSong-Regular(0)/m/n/7 实 验 结 果 表 明，|
 []


Underfull \hbox (badness 10000) in paragraph at lines 577--578
\TU/lmr/m/n/7 GM-Mamba \TU/FandolSong-Regular(0)/m/n/7 模
 []


Underfull \hbox (badness 1502) in paragraph at lines 577--578
\TU/FandolSong-Regular(0)/m/n/7 争 力。|  在 序 列 建
 []


Underfull \hbox (badness 10000) in paragraph at lines 577--578
\TU/FandolSong-Regular(0)/m/n/7 力。| \TU/lmr/m/n/7 Transformer
 []


Underfull \hbox (badness 4316) in paragraph at lines 577--578
\TU/FandolSong-Regular(0)/m/n/7 现 出 色，| \TU/lmr/m/n/7 AP \TU/FandolSong-Regular(0)/m/n/7 达
 []


Underfull \hbox (badness 3568) in paragraph at lines 577--578
\TU/FandolSong-Regular(0)/m/n/7 为 \TU/lmr/m/n/7 40.47%)\TU/FandolSong-Regular(0)/m/n/7 ，|  而
 []


Underfull \hbox (badness 3240) in paragraph at lines 577--578
\TU/FandolSong-Regular(0)/m/n/7 传 统 \TU/lmr/m/n/7 LSTM \TU/FandolSong-Regular(0)/m/n/7 和
 []


Underfull \hbox (badness 1502) in paragraph at lines 579--580
\TU/FandolSong-Regular(0)/m/n/7 现 最 优。|  值 得 注
 []


Underfull \hbox (badness 1502) in paragraph at lines 579--580
\TU/FandolSong-Regular(0)/m/n/7 意 的 是，|  小 波 变
 []


Underfull \hbox (badness 2426) in paragraph at lines 579--580
\TU/FandolSong-Regular(0)/m/n/7 低 \TU/lmr/m/n/7 0.33 \TU/FandolSong-Regular(0)/m/n/7 个 百 分
 []


Underfull \hbox (badness 10000) in paragraph at lines 583--583
 \TU/lmr/m/it/10 E. MPC-
 []


Underfull \hbox (badness 10000) in paragraph at lines 585--586
[]\TU/FandolSong-Regular(0)/m/n/7 为 了 系 统 评 估
 []


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:587: LaTeX Error: Not in outer par mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.587 \begin{table*}[htbp]
                          
You've lost some text.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Underfull \hbox (badness 1430) in paragraph at lines 619--620
\TU/lmr/m/n/7 AP50 \TU/FandolSong-Regular(0)/m/n/7 上 取 得 最
 []


Underfull \hbox (badness 1430) in paragraph at lines 621--622
\TU/lmr/m/n/7 AP75 \TU/FandolSong-Regular(0)/m/n/7 指 标 上 表
 []


Underfull \hbox (badness 10000) in paragraph at lines 623--624
[]\TU/FandolSong-Regular(0)/m/n/7 多 尺 度 约 束 策
 []


Underfull \hbox (badness 1430) in paragraph at lines 623--624
\TU/lmr/m/n/7 AP50 \TU/FandolSong-Regular(0)/m/n/7 上 表 现 最
 []


Underfull \hbox (badness 1502) in paragraph at lines 623--624
\TU/FandolSong-Regular(0)/m/n/7 表 现 优 异，|  分 别
 []


Underfull \hbox (badness 10000) in paragraph at lines 629--630
[]\TU/FandolSong-Regular(0)/m/n/7 为 了 全 面 评 估
 []


Underfull \hbox (badness 10000) in paragraph at lines 629--630
\TU/lmr/m/n/7 YL\TU/FandolSong-Regular(0)/m/n/7 、| \TU/lmr/m/n/7 QI\TU/FandolSong-Regular(0)/m/n/7 、| \TU/lmr/m/n/7 MR\TU/FandolSong-Regular(0)/m/n/7 、|
 []


Underfull \hbox (badness 10000) in paragraph at lines 629--630
\TU/lmr/m/n/7 SL\TU/FandolSong-Regular(0)/m/n/7 、| \TU/lmr/m/n/7 DT\TU/FandolSong-Regular(0)/m/n/7 、| \TU/lmr/m/n/7 MRT
 []


LaTeX Warning: Reference `tab:comparison_single' on page 2 undefined on input line 633.


Underfull \hbox (badness 10000) in paragraph at lines 633--634
 \TU/lmr/m/it/10 1) \TU/FandolSong-Regular(0)/m/it/10 单 一 林
 []


Underfull \hbox (badness 10000) in paragraph at lines 633--634
\TU/FandolSong-Regular(0)/m/it/10 分 对 比 实
 []


Underfull \hbox (badness 7685) in paragraph at lines 633--634
\TU/FandolSong-Regular(0)/m/it/10 验\TU/lmr/m/it/10 : [][]\TU/FandolSong-Regular(0)/m/n/7 表[]展 示
 []


Underfull \hbox (badness 10000) in paragraph at lines 633--634
\TU/lmr/m/n/7 63.94%\TU/FandolSong-Regular(0)/m/n/7 ，| \TU/lmr/m/n/7 AP50
 []


Underfull \hbox (badness 2426) in paragraph at lines 633--634
\TU/FandolSong-Regular(0)/m/n/7 和 \TU/lmr/m/n/7 0.06 \TU/FandolSong-Regular(0)/m/n/7 个 百 分
 []


Underfull \hbox (badness 1502) in paragraph at lines 633--634
\TU/FandolSong-Regular(0)/m/n/7 点。|  值 得 注 意 的
 []


Underfull \hbox (badness 3019) in paragraph at lines 633--634
\TU/FandolSong-Regular(0)/m/n/7 标 上，| \TU/lmr/m/n/7 CSAF \TU/FandolSong-Regular(0)/m/n/7 达
 []


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:635: LaTeX Error: Not in outer par mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.635 \begin{table*}[htbp]
                          
You've lost some text.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Underfull \hbox (badness 2426) in paragraph at lines 662--663
\TU/FandolSong-Regular(0)/m/n/7 和 \TU/lmr/m/n/7 6.30 \TU/FandolSong-Regular(0)/m/n/7 个 百 分
 []


LaTeX Warning: Reference `tab:comparison_mixed' on page 2 undefined on input line 666.


Underfull \hbox (badness 1502) in paragraph at lines 666--667
\TU/FandolSong-Regular(0)/m/n/7 的 泛 化 能 力，|  我
 []


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:668: LaTeX Error: Not in outer par mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.668 \begin{table*}[htbp]
                          
You've lost some text.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Underfull \hbox (badness 1502) in paragraph at lines 695--696
\TU/FandolSong-Regular(0)/m/n/7 挑 战。|  值 得 注 意
 []


Underfull \hbox (badness 2426) in paragraph at lines 697--698
\TU/FandolSong-Regular(0)/m/n/7 和 \TU/lmr/m/n/7 7.04 \TU/FandolSong-Regular(0)/m/n/7 个 百 分
 []


LaTeX Warning: Reference `fig:counting_results' on page 2 undefined on input line 701.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:703: LaTeX Error: Not in outer par mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.703 \begin{figure*}[htbp]
                           
You've lost some text.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


LaTeX Warning: File `fig/counting_comparison.pdf' not found on input line 705.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:705: Unable to load picture or PDF file 'fig/counting_comparison.pdf'.
<to be read again> 
                   }
l.705 ...9\textwidth]{fig/counting_comparison.pdf}
                                                  
The requested image couldn't be read because
it was not a recognized image format.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:705: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.705 ...9\textwidth]{fig/counting_comparison.pdf}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

File: fig/counting_comparison.pdf Graphic file (type pdf)
<use fig/counting_comparison.pdf>
e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:705: Unable to load picture or PDF file 'fig/counting_comparison.pdf'.
<to be read again> 
                   \GPT@clipend 
l.705 ...9\textwidth]{fig/counting_comparison.pdf}
                                                  
The requested image couldn't be read because
it was not a recognized image format.


Underfull \hbox (badness 1502) in paragraph at lines 710--711
\TU/FandolSong-Regular(0)/m/n/7 期  |— —|  树 冠 光 谱
 []


LaTeX Warning: Reference `fig:mixed_forest_counting' on page 2 undefined on input line 716.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:718: LaTeX Error: Not in outer par mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.718 \begin{figure*}[htbp]
                           
You've lost some text.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


LaTeX Warning: File `fig/mixed_forest_counting.pdf' not found on input line 720.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:720: Unable to load picture or PDF file 'fig/mixed_forest_counting.pdf'.
<to be read again> 
                   }
l.720 ...textwidth]{fig/mixed_forest_counting.pdf}
                                                  
The requested image couldn't be read because
it was not a recognized image format.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:720: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.720 ...textwidth]{fig/mixed_forest_counting.pdf}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

File: fig/mixed_forest_counting.pdf Graphic file (type pdf)
<use fig/mixed_forest_counting.pdf>
e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:720: Unable to load picture or PDF file 'fig/mixed_forest_counting.pdf'.
<to be read again> 
                   \GPT@clipend 
l.720 ...textwidth]{fig/mixed_forest_counting.pdf}
                                                  
The requested image couldn't be read because
it was not a recognized image format.


Underfull \hbox (badness 1502) in paragraph at lines 725--726
\TU/FandolSong-Regular(0)/m/n/7 棒 性。|  结 合 可 视
 []


Overfull \hbox (62.124pt too wide) detected at line 735
[][][][] \OT1/cmr/m/n/7 = 1 \OMS/cmsy/m/n/7 ^^@ []
 []


Underfull \hbox (badness 6110) in paragraph at lines 737--738
[]\TU/FandolSong-Regular(0)/m/n/7 其 中，| \TU/lmr/m/n/7 Predicted
 []


Underfull \hbox (badness 2229) in paragraph at lines 761--761
 \TU/lmr/m/it/10 B. SCVI \TU/FandolSong-Regular(0)/m/it/10 方
 []


Underfull \hbox (badness 10000) in paragraph at lines 775--776
[]\TU/FandolSong-Regular(0)/m/n/7 第 三，|  虽
 []


Underfull \hbox (badness 10000) in paragraph at lines 775--776
\TU/FandolSong-Regular(0)/m/n/7 然 \TU/lmr/m/n/7 MASA-
 []


Underfull \hbox (badness 3343) in paragraph at lines 775--776
\TU/lmr/m/n/7 Optimizer \TU/FandolSong-Regular(0)/m/n/7 模 块
 []


Underfull \hbox (badness 4266) in paragraph at lines 781--782
\TU/FandolSong-Regular(0)/m/n/7 界 增 强 的 \TU/lmr/m/n/7 GM-
 []


Underfull \hbox (badness 3343) in paragraph at lines 781--782
\TU/lmr/m/n/7 Optimizer \TU/FandolSong-Regular(0)/m/n/7 和 用
 []


Overfull \hbox (2.4335pt too wide) in paragraph at lines 789--790
|\TU/FandolSong-Regular(0)/m/n/7 （\TU/lmr/m/n/7 1630032022007\TU/FandolSong-Regular(0)/m/n/7 ） 、|
 []


Overfull \hbox (11.5475pt too wide) in paragraph at lines 789--790
|\TU/FandolSong-Regular(0)/m/n/7 （\TU/lmr/m/n/7 2019YFD1000500\TU/FandolSong-Regular(0)/m/n/7 ） 、|
 []


Underfull \hbox (badness 10000) in paragraph at lines 789--790
\TU/lmr/m/n/7 KLOF202305\TU/FandolSong-Regular(0)/m/n/7 ）|
 []

(ieee_tgrs_paper.bbl
Overfull \hbox (1.4145pt too wide) in paragraph at lines 2--2
  []\TU/lmr/m/sc/10 References 
 []


Underfull \hbox (badness 10000) in paragraph at lines 25--27
[]
 []


Overfull \hbox (5.64647pt too wide) in paragraph at lines 25--27
\TU/lmr/m/n/8 P. Priyadar-
 []


Underfull \hbox (badness 10000) in paragraph at lines 25--27
\TU/lmr/m/n/8 shan,
 []


Underfull \hbox (badness 10000) in paragraph at lines 25--27
\TU/lmr/m/it/8 Biology
 []


Underfull \hbox (badness 10000) in paragraph at lines 25--27
\TU/lmr/m/it/8 of Hevea
 []


Underfull \hbox (badness 10000) in paragraph at lines 25--27
\TU/lmr/m/it/8 rubber\TU/lmr/m/n/8 .
 []


Underfull \hbox (badness 10000) in paragraph at lines 25--27
\TU/lmr/m/n/8 CABI,
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
[]\TU/lmr/m/n/8 Y. Ke
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 Quacken-
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 bush, “A
 []


Underfull \hbox (badness 8094) in paragraph at lines 29--33
\TU/lmr/m/n/8 review of
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 methods
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 for
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 automatic
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 individual
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 tree-
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 crown
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 detection
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 and de-
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 lineation
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 from
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 passive
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 remote
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 sensing,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/it/8 Inter-
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/it/8 national
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/it/8 Journal
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/it/8 Sensing\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 vol. 32,
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 no. 17,
 []


Underfull \hbox (badness 10000) in paragraph at lines 29--33
\TU/lmr/m/n/8 4747,
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
[]\TU/lmr/m/n/8 F. A.
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/n/8 Gougeon,
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/n/8 “Auto-
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/n/8 matic
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/n/8 individual
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/n/8 delin-
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/n/8 eation
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/n/8 using a
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/n/8 valley-
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/n/8 following
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/n/8 algorithm
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/n/8 and a
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/n/8 rule-based
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/n/8 system,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 Proceed-
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 ings of
 []


Underfull \hbox (badness 1648) in paragraph at lines 35--39
\TU/lmr/m/it/8 the Inter-
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 national
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 Forum
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 on Au-
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 tomated
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 Interpre-
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 tation
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 of High
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 Spatial
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 Reso-
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 lution
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 Digital
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 Imagery
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 for
 []


Underfull \hbox (badness 10000) in paragraph at lines 35--39
\TU/lmr/m/it/8 Forestry\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
[]\TU/lmr/m/n/8 D. S.
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/n/8 Culvenor,
 []


Underfull \hbox (badness 1688) in paragraph at lines 41--44
\TU/lmr/m/n/8 “Tida: an
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/n/8 algorithm
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/n/8 lineation
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/n/8 of tree
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/n/8 crowns
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/n/8 in high
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/n/8 spatial
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/n/8 resolution
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/n/8 remotely
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/n/8 sensed
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/n/8 imagery,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/it/8 Com-
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/it/8 puters
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/it/8 & Geo-
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/it/8 sciences\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/n/8 vol. 28,
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--44
\TU/lmr/m/n/8 33–44,
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
[]\TU/lmr/m/n/8 M. Erik-
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 son,
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 “Segmen-
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 tation of
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 individual
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 tree
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 crowns
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 in colour
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 tographs
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 using
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 region
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 growing
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 supported
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 by fuzzy
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 rules,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/it/8 Canadian
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/it/8 Journal
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/it/8 of Forest
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/it/8 Research\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 vol. 33,
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 no. 8,
 []


Underfull \hbox (badness 10000) in paragraph at lines 46--49
\TU/lmr/m/n/8 1563,
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
[]\TU/lmr/m/n/8 L. Jing,
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 B. Hu,
 []


Overfull \hbox (0.67047pt too wide) in paragraph at lines 51--55
\TU/lmr/m/n/8 T. Noland,
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 “Auto-
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 mated
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 delin-
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 eation of
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 individual
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 tree
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 crowns
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 data by
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 multi-
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 scale
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 analysis
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 and
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 segmen-
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 tation,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/it/8 Pho-
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/it/8 togram-
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/it/8 metric
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/it/8 Engineer-
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/it/8 ing &
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/it/8 Remote
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/it/8 Sensing\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 vol. 78,
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 no. 11,
 []


Underfull \hbox (badness 10000) in paragraph at lines 51--55
\TU/lmr/m/n/8 1284,
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
[]\TU/lmr/m/n/8 J. Yang,
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 Z. Kang,
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 Z. Yang,
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 Akwensi,
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 “Auto-
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 mated
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 extraction
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 of
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 individual
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 airborne
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 laser
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 scanning
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 point
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 clouds in
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 forests,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/it/8 IEEE
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/it/8 Selected
 []


Underfull \hbox (badness 3884) in paragraph at lines 57--61
\TU/lmr/m/it/8 Topics in
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/it/8 Applied
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/it/8 Earth
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/it/8 Observa-
 []


Underfull \hbox (badness 3884) in paragraph at lines 57--61
\TU/lmr/m/it/8 tions and
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/it/8 Remote
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/it/8 Sensing\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 vol. 7,
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 no. 10,
 []


Underfull \hbox (badness 10000) in paragraph at lines 57--61
\TU/lmr/m/n/8 4094,
 []


Underfull \hbox (badness 5711) in paragraph at lines 63--66
[]\TU/lmr/m/n/8 L. Wang,
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 P. Gong,
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 Biging,
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 “Auto-
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 mated
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 delin-
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 eation
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 of tree
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 crowns
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 spatial
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 resolution
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 aerial
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 imagery,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/it/8 Pho-
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/it/8 togram-
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/it/8 metric
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/it/8 Engineer-
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/it/8 ing &
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/it/8 Remote
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/it/8 Sensing\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 vol. 70,
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 no. 9,
 []


Underfull \hbox (badness 10000) in paragraph at lines 63--66
\TU/lmr/m/n/8 1052,
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/n/8 dividual
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/n/8 tree-
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/n/8 crown
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/n/8 delin-
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/n/8 treetop
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/n/8 detection
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/n/8 in high-
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/n/8 spatial-
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/n/8 resolution
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/n/8 aerial
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/n/8 imagery,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/it/8 Pho-
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/it/8 togram-
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/it/8 metric
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/it/8 Engineer-
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/it/8 ing &
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/it/8 Remote
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/it/8 Sensing\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/n/8 vol. 70,
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--71
\TU/lmr/m/n/8 351–357,
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
[]\TU/lmr/m/n/8 W. R.
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 Lamar,
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 J. B.
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 McGraw,
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 Warner,
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 “Auto-
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 mated
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 detection
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 of
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 individual
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 tree
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 crowns
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 in high
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 resolution
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 aerial
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 imagery
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 using
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 multiple-
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 scale
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 filtering
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 and
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 template
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 match-
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/n/8 ing,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/it/8 Proceed-
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/it/8 ASPRS
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/it/8 2005
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/it/8 Annual
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/it/8 Confer-
 []


Underfull \hbox (badness 10000) in paragraph at lines 73--77
\TU/lmr/m/it/8 ence\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
[]\TU/lmr/m/n/8 X. Tong,
 []


Underfull \hbox (badness 2150) in paragraph at lines 79--83
\TU/lmr/m/n/8 K. Wang,
 []


Overfull \hbox (1.97447pt too wide) in paragraph at lines 79--83
\TU/lmr/m/n/8 M. Brandt,
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 Y. Yue,
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 C. Liao,
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 and
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 R. Fen-
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 sholt,
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 “Im-
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 proved
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 watershed
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 segmen-
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 tation
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 algorithm
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 for
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 individual
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 delin-
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 eation
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 using
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 airborne
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 cloud
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 data,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/it/8 IEEE
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/it/8 Trans-
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/it/8 actions
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/it/8 on Geo-
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/it/8 science
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/it/8 and
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/it/8 Remote
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/it/8 Sensing\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 79--83
\TU/lmr/m/n/8 vol. 60,
 []


Underfull \hbox (badness 6141) in paragraph at lines 79--83
\TU/lmr/m/n/8 pp. 1–16,
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
[]\TU/lmr/m/n/8 K. Zhao,
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/n/8 J. C.
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/n/8 Suarez,
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/n/8 M. Gar-
 []


Underfull \hbox (badness 3168) in paragraph at lines 85--89
\TU/lmr/m/n/8 C. Wang,
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/n/8 compre-
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/n/8 hensive
 []


Underfull \hbox (badness 8094) in paragraph at lines 85--89
\TU/lmr/m/n/8 review of
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/n/8 individual
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/n/8 detection
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/n/8 and de-
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/n/8 lineation
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/n/8 learning,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/it/8 ISPRS
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/it/8 Journal
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/it/8 of Pho-
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/it/8 togram-
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/it/8 Remote
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/it/8 Sensing\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/n/8 vol. 196,
 []


Underfull \hbox (badness 10000) in paragraph at lines 85--89
\TU/lmr/m/n/8 pp. 233–
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
[]\TU/lmr/m/n/8 G. Las-
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 salle,
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 M. P.
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 Ferreira,
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 F. R.
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 Pereira,
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 Sarrailh,
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 “Cnn-
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 based
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 individual
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 delin-
 []


Underfull \hbox (badness 4479) in paragraph at lines 91--94
\TU/lmr/m/n/8 eation on
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 hyper-
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 spectral
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 images,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/it/8 IEEE
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/it/8 Geo-
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/it/8 science
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/it/8 and
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/it/8 Remote
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/it/8 Sensing
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/it/8 Letters\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 vol. 19,
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--94
\TU/lmr/m/n/8 pp. 1–5,
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
[]
 []


Overfull \hbox (5.98247pt too wide) in paragraph at lines 96--100
\TU/lmr/m/n/8 M. Freuden-
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 berg,
 []


Underfull \hbox (badness 3168) in paragraph at lines 96--100
\TU/lmr/m/n/8 N. Nölke,
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 A. Agos-
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 tini,
 []


Overfull \hbox (11.89447pt too wide) in paragraph at lines 96--100
\TU/lmr/m/n/8 F. Wörgötter,
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 and
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 “Individ-
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 ual tree
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 lineation
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 in high-
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 resolution
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 remote
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 sensing
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 images
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 based on
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 u-net,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/it/8 Neural
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/it/8 Comput-
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/it/8 ing and
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/it/8 Appli-
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/it/8 cations\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 vol. 34,
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 19917–
 []


Underfull \hbox (badness 10000) in paragraph at lines 96--100
\TU/lmr/m/n/8 19932,
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
[]\TU/lmr/m/n/8 O. Ron-
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/n/8 neberger,
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/n/8 P. Fis-
 []


Underfull \hbox (badness 5260) in paragraph at lines 102--106
\TU/lmr/m/n/8 cher, and
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/n/8 T. Brox,
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/n/8 “U-net:
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/n/8 Convo-
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/n/8 lutional
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/n/8 networks
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/n/8 for
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/n/8 biomedi-
 []


Underfull \hbox (badness 3148) in paragraph at lines 102--106
\TU/lmr/m/n/8 cal image
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/n/8 segmen-
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/n/8 tation,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/n/8 in \TU/lmr/m/it/8 Inter-
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/it/8 national
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/it/8 Confer-
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/it/8 ence on
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/it/8 Medical
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/it/8 image
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/it/8 computing
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/it/8 and
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/it/8 computer-
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/it/8 assisted
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/it/8 inter-
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/it/8 vention\TU/lmr/m/n/8 .
 []


Underfull \hbox (badness 10000) in paragraph at lines 102--106
\TU/lmr/m/n/8 Springer,
 []


Underfull \hbox (badness 6141) in paragraph at lines 102--106
\TU/lmr/m/n/8 2015, pp.
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
[]\TU/lmr/m/n/8 K. He,
 []


Overfull \hbox (6.47847pt too wide) in paragraph at lines 108--111
\TU/lmr/m/n/8 G. Gkioxari,
 []


Underfull \hbox (badness 1688) in paragraph at lines 108--111
\TU/lmr/m/n/8 P. Dollár,
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
\TU/lmr/m/n/8 and
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
\TU/lmr/m/n/8 R. Gir-
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
\TU/lmr/m/n/8 shick,
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
\TU/lmr/m/n/8 “Mask
 []


Underfull \hbox (badness 1675) in paragraph at lines 108--111
\TU/lmr/m/n/8 r-cnn,” in
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
\TU/lmr/m/it/8 Proceed-
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
\TU/lmr/m/it/8 ings of
 []


Underfull \hbox (badness 3525) in paragraph at lines 108--111
\TU/lmr/m/it/8 the IEEE
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
\TU/lmr/m/it/8 interna-
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
\TU/lmr/m/it/8 tional
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
\TU/lmr/m/it/8 confer-
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
\TU/lmr/m/it/8 ence on
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
\TU/lmr/m/it/8 computer
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
\TU/lmr/m/it/8 vision\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 6141) in paragraph at lines 108--111
\TU/lmr/m/n/8 2017, pp.
 []


Underfull \hbox (badness 10000) in paragraph at lines 108--111
\TU/lmr/m/n/8 2961–
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
[]\TU/lmr/m/n/8 J. R.
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 Braga,
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 V. Peri-
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 pato,
 []


Overfull \hbox (7.04646pt too wide) in paragraph at lines 113--117
\TU/lmr/m/n/8 R. Dalagnol,
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 M. P.
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 Ferreira,
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 Y. Tara-
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 balka,
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 L. E.
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 Aragão,
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 H. F.
 []


Overfull \hbox (4.50247pt too wide) in paragraph at lines 113--117
\TU/lmr/m/n/8 de Campos-
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 Velho,
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 E. H.
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 Shigue-
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 F. H.
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 Wagner,
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 “Tree
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 lineation
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 algorithm
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 based on
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 a convo-
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 lutional
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 neural
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 network,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/it/8 Remote
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/it/8 Sensing\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 vol. 12,
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 no. 8,
 []


Underfull \hbox (badness 10000) in paragraph at lines 113--117
\TU/lmr/m/n/8 p. 1288,
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
[]\TU/lmr/m/n/8 Z. Hao,
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 L. Lin,
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 E. A.
 []


Overfull \hbox (4.2625pt too wide) in paragraph at lines 119--124
\TU/lmr/m/n/8 Mikhailova,
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 M. Li,
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 Y. Chen,
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 K. Yu,
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 and
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 J. Liu,
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 “Individ-
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 ual tree
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 crown
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 detection
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 and de-
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 lineation
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 from
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 very-high-
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 resolution
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 uav
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 images
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 based
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 on bias
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 field and
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 marker-
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 controlled
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 watershed
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 segmen-
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 tation
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 algo-
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 rithms,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/it/8 Remote
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/it/8 Sensing\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 vol. 13,
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 no. 7,
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--124
\TU/lmr/m/n/8 p. 1298,
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 S. H.
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 Hickman,
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 T. D.
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 Jackson,
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 X. J.
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 Koay,
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 J. Hirst,
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 W. Jay,
 []


Overfull \hbox (0.67847pt too wide) in paragraph at lines 126--131
\TU/lmr/m/n/8 M. Archer,
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 Kientz,
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 G. Vin-
 []


Underfull \hbox (badness 5288) in paragraph at lines 126--131
\TU/lmr/m/n/8 cent, and
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 D. A.
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 Coomes,
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 “Detec-
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 tree2: A
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 software
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 package
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 for au-
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 tomated
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 lineation
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 in high-
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 resolution
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 aerial
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 imagery,”
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/it/8 Methods
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/it/8 and
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/it/8 Evolution\TU/lmr/m/n/8 ,
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 vol. 14,
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 no. 4,
 []


Underfull \hbox (badness 10000) in paragraph at lines 126--131
\TU/lmr/m/n/8 1045,
 []

)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:794: LaTeX Error: \begin{tabular} on input line 105 ended by \end{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.794 \end{document}
                    
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

(ieee_tgrs_paper.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2022/08/05>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.


LaTeX Warning: There were undefined references.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:794: You can't use `\end' in internal vertical mode.
\enddocument ...cument/end}\deadcycles \z@ \@@end 
                                                  
l.794 \end{document}
                    
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:794: LaTeX Error: \begin{tabular} on input line 105 ended by \end{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.794 \end{document}
                    
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:794: Missing } inserted.
<inserted text> 
                }
l.794 \end{document}
                    
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:794: Missing } inserted.
<inserted text> 
                }
l.794 \end{document}
                    
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.796 \subsection
                 {BT-Set数据集对比实验}
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.796 \subsection
                 {BT-Set数据集对比实验}
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing } inserted.
<inserted text> 
                }
l.796 \subsection{BT-Set数据集对比实验}
                                
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing { inserted.
<inserted text> 
                {
l.796 \subsection{BT-Set数据集对比实验}
                                
I've put in what seems to be necessary to fix
the current column of the current alignment.
Try to go on, since this might almost work.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing { inserted.
<inserted text> 
                {
l.796 \subsection{BT-Set数据集对比实验}
                                
I've put in what seems to be necessary to fix
the current column of the current alignment.
Try to go on, since this might almost work.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Improper \prevdepth.
\tab@epar ...p \par \fi \unskip \ifdim \prevdepth 
                                                  >-\@m \p@ \ifdim \prevdept...
l.796 \subsection{BT-Set数据集对比实验}
                                
You can refer to \spacefactor only in horizontal mode;
you can refer to \prevdepth only in vertical mode; and
neither of these is meaningful inside \write. So
I'm forgetting what you said and using zero instead.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Improper \prevdepth.
\tab@epar ...revdepth >-\@m \p@ \ifdim \prevdepth 
                                                  <\dp \@arstrutbox \kern -\...
l.796 \subsection{BT-Set数据集对比实验}
                                
You can refer to \spacefactor only in horizontal mode;
you can refer to \prevdepth only in vertical mode; and
neither of these is meaningful inside \write. So
I'm forgetting what you said and using zero instead.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Improper \prevdepth.
\tab@epar ... <\dp \@arstrutbox \kern -\prevdepth 
                                                  \nointerlineskip \vtop to\...
l.796 \subsection{BT-Set数据集对比实验}
                                
You can refer to \spacefactor only in horizontal mode;
you can refer to \prevdepth only in vertical mode; and
neither of these is meaningful inside \write. So
I'm forgetting what you said and using zero instead.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: You can't use `\prevdepth' in restricted horizontal mode.
\nointerlineskip ->\prevdepth 
                              -\@m \p@ 
l.796 \subsection{BT-Set数据集对比实验}
                                
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing $ inserted.
<inserted text> 
                $
l.796 \subsection{BT-Set数据集对比实验}
                                
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing number, treated as zero.
<to be read again> 
                   \vtop 
l.796 \subsection{BT-Set数据集对比实验}
                                
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Illegal unit of measure (pt inserted).
<to be read again> 
                   \vtop 
l.796 \subsection{BT-Set数据集对比实验}
                                
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Extra }, or forgotten $.
\tab@epar ...to\dp \@arstrutbox {}\fi \fi \egroup 
                                                  \ifdim \ht \z@ <\ht \@arst...
l.796 \subsection{BT-Set数据集对比实验}
                                
I've deleted a group-closing symbol because it seems to be
spurious, as in `$x}$'. But perhaps the } is legitimate and
you forgot something else, as in `\hbox{$x}'. In such cases
the way to recover is to insert both the forgotten and the
deleted material, e.g., by typing `I$}'.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing $ inserted.
<inserted text> 
                $
l.796 \subsection{BT-Set数据集对比实验}
                                
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

Missing character: There is no è in font cmex7!
e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing } inserted.
<inserted text> 
                }
l.796 \subsection{BT-Set数据集对比实验}
                                
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing } inserted.
<inserted text> 
                }
l.796 \subsection{BT-Set数据集对比实验}
                                
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing } inserted.
<inserted text> 
                }
l.796 \subsection{BT-Set数据集对比实验}
                                
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
I'm guessing that you meant to end an alignment here.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Misplaced \cr.
<inserted text> \cr 
                    
l.796 \subsection{BT-Set数据集对比实验}
                                
I can't figure out why you would want to use a tab mark
or \cr or \span just now. If something like a right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:796: Missing \cr inserted.
<inserted text> 
                \cr 
l.796 \subsection{BT-Set数据集对比实验}
                                
(That makes 100 errors; please try again.) 
Here is how much of TeX's memory you used:
 16548 strings out of 409617
 356336 string characters out of 5778277
 2217191 words of memory out of 5000000
 38397 multiletter control sequences out of 15000+600000
 563818 words of font info for 112 fonts, out of 8000000 for 9000
 1351 hyphenation exceptions out of 8191
 79i,25n,93p,600b,799s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on ieee_tgrs_paper.xdv (1 page, 49700 bytes).
