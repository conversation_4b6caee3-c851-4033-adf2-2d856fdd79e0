This is XeTeX, Version 3.141592653-2.6-0.999995 (MiKTeX 24.1) (preloaded format=xelatex 2025.8.1)  3 AUG 2025 17:57
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex
(e:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(C:\Program Files\MiKTeX\tex/latex/ieeetran\IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count183
\@IEEEtrantmpcountB=\count184
\@IEEEtrantmpcountC=\count185
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 503.
LaTeX Font Info:    No file TUptm.fd. on input line 503.

LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 503.

-- Using 8.5in x 11in (letter) paper.
-- Using DVI output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146

LaTeX Font Warning: Font shape `TU/ptm/bx/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/bx/it' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 1090.

\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@IEEEsubequation=\count190
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count191
\c@table=\count192
\@IEEEeqnnumcols=\count193
\@IEEEeqncolcnt=\count194
\@IEEEsubeqnnumrollback=\count195
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count196
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count197
\@IEEEtranrubishbin=\box52
) (C:\Program Files\MiKTeX\tex/latex/cite\cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (C:\Program Files\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (C:\Program Files\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks18
) (C:\Program Files\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (C:\Program Files\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (C:\Program Files\MiKTeX\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
 (C:\Program Files\MiKTeX\tex/latex/graphics-def\xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen164
\Gin@req@width=\dimen165
) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(C:\Program Files\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (C:\Program Files\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen166
)) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen167
) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count198
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count199
\leftroot@=\count266
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count267
\DOTSCASE@=\count268
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen168
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count269
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count270
\dotsspace@=\muskip16
\c@parentequation=\count271
\dspbrk@lvl=\count272
\tag@help=\toks20
\row@=\count273
\column@=\count274
\maxfields@=\count275
\andhelp@=\toks21
\eqnshift@=\dimen169
\alignsep@=\dimen170
\tagshift@=\dimen171
\tagwidth@=\dimen172
\totwidth@=\dimen173
\lineht@=\dimen174
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (C:\Program Files\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (C:\Program Files\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (C:\Program Files\MiKTeX\tex/latex/algorithms\algorithm.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (C:\Program Files\MiKTeX\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count276
\float@exts=\toks24
\float@box=\box55
\@float@everytoks=\toks25
\@floatcapt=\box56
) (C:\Program Files\MiKTeX\tex/latex/base\ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks26
\c@algorithm=\count277
) (C:\Program Files\MiKTeX\tex/latex/algorithms\algorithmic.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
\c@ALC@unique=\count278
\c@ALC@line=\count279
\c@ALC@rem=\count280
\c@ALC@depth=\count281
\ALC@tlm=\skip54
\algorithmicindent=\skip55
) (C:\Program Files\MiKTeX\tex/latex/tools\array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen175
\ar@mcellbox=\box57
\extrarowheight=\dimen176
\NC@list=\toks27
\extratabsurround=\skip56
\backup@length=\skip57
\ar@cellbox=\box58
) (C:\Program Files\MiKTeX\tex/latex/mdwtools\mdwmath.sty
Package: mdwmath 1996/04/11 1.1 Nice mathematical things
\sq@sqrt=\count282
LaTeX Info: Redefining \sqrt on input line 84.
) (C:\Program Files\MiKTeX\tex/latex/mdwtools\mdwtab.sty
Package: mdwtab 1998/04/28 1.9 Table typesetting with style
\tab@state=\count283
\tab@columns=\count284
\tab@preamble=\toks28
\tab@shortline=\toks29
\extrarowheight=\dimen177
\tabextrasep=\dimen178
\arrayextrasep=\dimen179
\smarraycolsep=\dimen180
\smarrayextrasep=\dimen181
\tab@width=\dimen182
\col@sep=\dimen183
\tab@endheight=\dimen184
\tab@leftskip=\skip58
\tab@rightskip=\skip59
\fn@notes=\box59
\fn@width=\dimen185
) (C:\Program Files\MiKTeX\tex/latex/eqparbox\eqparbox.sty
Package: eqparbox 2017/09/03 v4.1 Create equal-widthed boxes
\eqp@tempdima=\skip60
\eqp@tempdimb=\skip61
\eqp@tabular@box=\box60
\eqp@list@box=\box61
\eqp@list@indent=\skip62
 (C:\Program Files\MiKTeX\tex/latex/environ\environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments
 (C:\Program Files\MiKTeX\tex/latex/trimspaces\trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
))) (C:\Program Files\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (C:\Program Files\MiKTeX\tex/latex/subfigure\subfigure.sty
Package: subfigure 2002/07/30 v2.1.4 subfigure package
\subfigtopskip=\skip63
\subfigcapskip=\skip64
\subfigcaptopadj=\dimen186
\subfigbottomskip=\skip65
\subfigcapmargin=\dimen187
\subfiglabelskip=\skip66
\c@subfigure=\count285
\c@lofdepth=\count286
\c@subtable=\count287
\c@lotdepth=\count288

****************************************
* Local config file subfigure.cfg used *
****************************************
(C:\Program Files\MiKTeX\tex/latex/subfigure\subfigure.cfg)
\subfig@top=\skip67
\subfig@bottom=\skip68
) (C:\Program Files\MiKTeX\tex/latex/multirow\multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip69
\multirow@cntb=\count289
\multirow@dima=\skip70
\bigstrutjot=\dimen188
) (C:\Program Files\MiKTeX\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen189
\lightrulewidth=\dimen190
\cmidrulewidth=\dimen191
\belowrulesep=\dimen192
\belowbottomsep=\dimen193
\aboverulesep=\dimen194
\abovetopsep=\dimen195
\cmidrulesep=\dimen196
\cmidrulekern=\dimen197
\defaultaddspace=\dimen198
\@cmidla=\count290
\@cmidlb=\count291
\@aboverulesep=\dimen199
\@belowrulesep=\dimen256
\@thisruleclass=\count292
\@lastruleclass=\count293
\@thisrulewidth=\dimen257
) (C:\Program Files\MiKTeX\tex/latex/graphics\color.sty
Package: color 2022/01/06 v1.3d Standard LaTeX Color (DPC)
 (C:\Program Files\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: xetex.def on input line 149.
 (C:\Program Files\MiKTeX\tex/latex/graphics\mathcolor.ltx)) (C:\Program Files\MiKTeX\tex/latex/preprint\balance.sty
Package: balance 1999/02/23 4.3 (PWD)
\oldvsize=\dimen258
) (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec.sty (C:\Program Files\MiKTeX\tex/latex/l3packages/xparse\xparse.sty (C:\Program Files\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2024-01-04 L3 programming layer (loader) 
 (C:\Program Files\MiKTeX\tex/latex/l3backend\l3backend-xetex.def
File: l3backend-xetex.def 2024-01-04 L3 backend support: XeTeX
\g__graphics_track_int=\count294
\l__pdf_internal_box=\box62
\g__pdf_backend_object_int=\count295
\g__pdf_backend_annotation_int=\count296
\g__pdf_backend_link_int=\count297
))
Package: xparse 2023-10-10 L3 Experimental document command parser
)
Package: fontspec 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
 (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec-xetex.sty
Package: fontspec-xetex 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count298
\l__fontspec_language_int=\count299
\l__fontspec_strnum_int=\count300
\l__fontspec_tmp_int=\count301
\l__fontspec_tmpa_int=\count302
\l__fontspec_tmpb_int=\count303
\l__fontspec_tmpc_int=\count304
\l__fontspec_em_int=\count305
\l__fontspec_emdef_int=\count306
\l__fontspec_strong_int=\count307
\l__fontspec_strongdef_int=\count308
\l__fontspec_tmpa_dim=\dimen259
\l__fontspec_tmpb_dim=\dimen260
\l__fontspec_tmpc_dim=\dimen261
 (C:\Program Files\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec.cfg))) (C:\Program Files\MiKTeX\tex/xelatex/xecjk\xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX
 (C:\Program Files\MiKTeX\tex/latex/ctex\ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (C:\Program Files\MiKTeX\tex/latex/l3packages/xtemplate\xtemplate.sty
Package: xtemplate 2023-10-10 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen262
\l__xtemplate_tmp_int=\count309
\l__xtemplate_tmp_muskip=\muskip18
\l__xtemplate_tmp_skip=\skip71
)
\l__xeCJK_tmp_int=\count310
\l__xeCJK_tmp_box=\box63
\l__xeCJK_tmp_dim=\dimen263
\l__xeCJK_tmp_skip=\skip72
\g__xeCJK_space_factor_int=\count311
\l__xeCJK_begin_int=\count312
\l__xeCJK_end_int=\count313
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip73
\c__xeCJK_none_node=\count314
\g__xeCJK_node_int=\count315
\c__xeCJK_CJK_node_dim=\dimen264
\c__xeCJK_CJK-space_node_dim=\dimen265
\c__xeCJK_default_node_dim=\dimen266
\c__xeCJK_CJK-widow_node_dim=\dimen267
\c__xeCJK_normalspace_node_dim=\dimen268
\c__xeCJK_default-space_node_skip=\skip74
\l__xeCJK_ccglue_skip=\skip75
\l__xeCJK_ecglue_skip=\skip76
\l__xeCJK_punct_kern_skip=\skip77
\l__xeCJK_indent_box=\box64
\l__xeCJK_last_penalty_int=\count316
\l__xeCJK_last_bound_dim=\dimen269
\l__xeCJK_last_kern_dim=\dimen270
\l__xeCJK_widow_penalty_int=\count317

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen271
\l__xeCJK_mixed_punct_width_dim=\dimen272
\l__xeCJK_middle_punct_width_dim=\dimen273
\l__xeCJK_fixed_margin_width_dim=\dimen274
\l__xeCJK_mixed_margin_width_dim=\dimen275
\l__xeCJK_middle_margin_width_dim=\dimen276
\l__xeCJK_bound_punct_width_dim=\dimen277
\l__xeCJK_bound_margin_width_dim=\dimen278
\l__xeCJK_margin_minimum_dim=\dimen279
\l__xeCJK_kerning_total_width_dim=\dimen280
\l__xeCJK_same_align_margin_dim=\dimen281
\l__xeCJK_different_align_margin_dim=\dimen282
\l__xeCJK_kerning_margin_width_dim=\dimen283
\l__xeCJK_kerning_margin_minimum_dim=\dimen284
\l__xeCJK_bound_dim=\dimen285
\l__xeCJK_reverse_bound_dim=\dimen286
\l__xeCJK_margin_dim=\dimen287
\l__xeCJK_minimum_bound_dim=\dimen288
\l__xeCJK_kerning_margin_dim=\dimen289
\g__xeCJK_family_int=\count318
\l__xeCJK_fam_int=\count319
\g__xeCJK_fam_allocation_int=\count320
\l__xeCJK_verb_case_int=\count321
\l__xeCJK_verb_exspace_skip=\skip78
 (C:\Program Files\MiKTeX\tex/xelatex/xecjk\xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
)) (C:\Program Files\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2023-11-26 v7.01g Hypertext links for LaTeX
 (C:\Program Files\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
) (C:\Program Files\MiKTeX\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (C:\Program Files\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (C:\Program Files\MiKTeX\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (C:\Program Files\MiKTeX\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (C:\Program Files\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (C:\Program Files\MiKTeX\tex/generic/pdftexcmds\pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (C:\Program Files\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (C:\Program Files\MiKTeX\tex/latex/letltxmacro\letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
) (C:\Program Files\MiKTeX\tex/latex/auxhook\auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (C:\Program Files\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (C:\Program Files\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (C:\Program Files\MiKTeX\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (C:\Program Files\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count322
) (C:\Program Files\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count323
)
\@linkdim=\dimen290
\Hy@linkcounter=\count324
\Hy@pagecounter=\count325
 (C:\Program Files\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2023-11-26 v7.01g Hyperref: PDFDocEncoding definition (HO)
) (C:\Program Files\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count326
 (C:\Program Files\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2023-11-26 v7.01g Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4064.
Package hyperref Info: Option `bookmarks' set `false' on input line 4064.
Package hyperref Info: Hyper figures OFF on input line 4181.
Package hyperref Info: Link nesting OFF on input line 4186.
Package hyperref Info: Hyper index ON on input line 4189.
Package hyperref Info: Plain pages OFF on input line 4196.
Package hyperref Info: Backreferencing OFF on input line 4201.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks OFF on input line 4454.
\c@Hy@tempcnt=\count327
LaTeX Info: Redefining \url on input line 4786.
\XeTeXLinkMargin=\dimen291
 (C:\Program Files\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (C:\Program Files\MiKTeX\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count328
\Field@Width=\dimen292
\Fld@charsize=\dimen293
Package hyperref Info: Hyper figures OFF on input line 6065.
Package hyperref Info: Link nesting OFF on input line 6070.
Package hyperref Info: Hyper index ON on input line 6073.
Package hyperref Info: backreferencing OFF on input line 6080.
Package hyperref Info: Link coloring ON on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6090.
Package hyperref Info: PDF/A mode OFF on input line 6095.
 (C:\Program Files\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count329
\c@Item=\count330
\c@Hfootnote=\count331
)
Package hyperref Info: Driver: hxetex.
 (C:\Program Files\MiKTeX\tex/latex/hyperref\hxetex.def
File: hxetex.def 2023-11-26 v7.01g Hyperref driver for XeTeX
 (C:\Program Files\MiKTeX\tex/generic/stringenc\stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box65
\c@Hy@AnnotLevel=\count332
\HyField@AnnotCount=\count333
\Fld@listcount=\count334
\c@bookmark@seq@number=\count335
 (C:\Program Files\MiKTeX\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)
 (C:\Program Files\MiKTeX\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 284.
)
\Hy@SectionHShift=\skip79
)

Package xeCJK Warning: Fandol is being set as the default font for CJK text.
(xeCJK)                Please make sure it has been properly installed.


Package fontspec Warning: Font "FandolSong-Regular" does not contain requested
(fontspec)                Script "CJK".


Package fontspec Info: Font family 'FandolSong-Regular(0)' created for font
(fontspec)             'FandolSong-Regular' with options
(fontspec)             [Script={CJK},Extension={.otf},BoldFont={FandolSong-Bold},ItalicFont={FandolKai-Regular}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[FandolSong-Regular.otf]/OT:language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"[FandolSong-Bold.otf]/OT:language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"[FandolKai-Regular.otf]/OT:language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 

(ieee_tgrs_paper.aux)
\openout1 = `ieee_tgrs_paper.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 30.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 30.
 (C:\Program Files\MiKTeX\tex/latex/base\ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.

-- Lines per column: 58 (exact).

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 30.
LaTeX Font Info:    Redeclaring math accent \acute on input line 30.
LaTeX Font Info:    Redeclaring math accent \grave on input line 30.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 30.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 30.
LaTeX Font Info:    Redeclaring math accent \bar on input line 30.
LaTeX Font Info:    Redeclaring math accent \breve on input line 30.
LaTeX Font Info:    Redeclaring math accent \check on input line 30.
LaTeX Font Info:    Redeclaring math accent \hat on input line 30.
LaTeX Font Info:    Redeclaring math accent \dot on input line 30.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 30.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 30.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 30.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 30.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 30.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 30.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 30.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 30.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 30.
Package hyperref Info: Link coloring ON on input line 30.

Underfull \hbox (badness 3679) in paragraph at lines 46--46
\TU/lmr/m/n/8 versity of Agriculture, City, State 12345 USA (e-mail: first-
 []


Underfull \hbox (badness 7613) in paragraph at lines 46--46
[][][]\TU/lmr/m/n/8 T. Lastname is with the Department of Computer Sci-
 []


Underfull \hbox (badness 3884) in paragraph at lines 46--46
\TU/lmr/m/n/8 ence, Technology University, City, State 12345 USA (e-mail:
 []


LaTeX Font Warning: Font shape `TU/FandolSong-Regular(0)/m/sc' undefined
(Font)              using `TU/FandolSong-Regular(0)/m/n' instead on input line 57.

[1


]
File: fig/Study Area.pdf Graphic file (type pdf)
<use fig/Study Area.pdf>
LaTeX Font Info:    Trying to load font information for U+msa on input line 103.
 (C:\Program Files\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 103.
 (C:\Program Files\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Overfull \hbox (17.16685pt too wide) detected at line 165
[][][][][]
 []

[2]
Underfull \hbox (badness 2310) in paragraph at lines 171--172
\TU/FandolSong-Regular(0)/m/n/10 先 通 过 拉 普 拉 斯 变 换 和 残 差 块 提 取 特 征 金 字 塔
 []


Underfull \hbox (badness 1418) in paragraph at lines 171--172
\OMS/cmsy/m/n/10 f\OML/cmm/m/it/10 F[]; F[]; F[]; F[]; F[]\OMS/cmsy/m/n/10 g$\TU/FandolSong-Regular(0)/m/n/10 ；|  然 后 \TU/ptm/bx/it/10 GM-Mamba \TU/FandolSong-Regular(0)/m/n/10 模 块 对 每
 []


Underfull \vbox (badness 10000) has occurred while \output is active []



Package amsmath Warning: Bracket group [X_1, X_2] at formula start!
(amsmath)                It could be a misspelled positional argument.
(amsmath)                If it belongs to the formula add a \relax in
(amsmath)                front to hide it on input line 232.

[3]
Package hyperref Info: bookmark level for unknown algorithm defaults to 0 on input line 264.

Underfull \hbox (badness 4518) in paragraph at lines 281--282
[] []\TU/FandolSong-Regular(0)/m/n/10 初 始 化 种 群：| $\OML/cmm/m/it/10 P \OMS/cmsy/m/n/10   f\OML/cmm/m/it/10 ^^K[]; [] ; ^^K[]\OMS/cmsy/m/n/10 g$\TU/FandolSong-Regular(0)/m/n/10 ，| $\OML/cmm/m/it/10 ^^K[] \OMS/cmsy/m/n/10 ^^X
 []


Underfull \hbox (badness 10000) in paragraph at lines 284--285
[] []\TU/FandolSong-Regular(0)/m/n/10 锦 标 赛 选 择：| $\OML/cmm/m/it/10 P[] \OMS/cmsy/m/n/10  
 []


Underfull \hbox (badness 10000) in paragraph at lines 292--293
[] []\TU/FandolSong-Regular(0)/m/n/10 更 新 经 验 缓 冲 区：| $\OMS/cmsy/m/n/10 B   B [
 []

(ieee_tgrs_paper.bbl [4]) [5] [6

] (ieee_tgrs_paper.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2022/08/05>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.

 ) 
Here is how much of TeX's memory you used:
 15895 strings out of 409617
 332625 string characters out of 5778277
 2003191 words of memory out of 5000000
 37748 multiletter control sequences out of 15000+600000
 564234 words of font info for 115 fonts, out of 8000000 for 9000
 1351 hyphenation exceptions out of 8191
 79i,14n,93p,601b,652s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on ieee_tgrs_paper.xdv (6 pages, 226472 bytes).
